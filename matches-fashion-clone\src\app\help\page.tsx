import Link from 'next/link';

export default function HelpPage() {
  const helpCategories = [
    {
      title: 'Orders & Delivery',
      links: [
        { name: 'Track your order', href: '/help/track-order' },
        { name: 'Delivery information', href: '/delivery' },
        { name: 'Delivery charges', href: '/help/delivery-charges' },
        { name: 'International delivery', href: '/help/international-delivery' },
      ]
    },
    {
      title: 'Returns & Exchanges',
      links: [
        { name: 'Return policy', href: '/returns' },
        { name: 'How to return', href: '/help/how-to-return' },
        { name: 'Exchanges', href: '/help/exchanges' },
        { name: 'Refunds', href: '/help/refunds' },
      ]
    },
    {
      title: 'Account & Shopping',
      links: [
        { name: 'Create an account', href: '/help/create-account' },
        { name: 'Manage your account', href: '/help/manage-account' },
        { name: 'Size guide', href: '/size-guide' },
        { name: 'Product care', href: '/help/product-care' },
      ]
    },
    {
      title: 'Payment & Pricing',
      links: [
        { name: 'Payment methods', href: '/help/payment-methods' },
        { name: 'Currency & pricing', href: '/help/currency-pricing' },
        { name: 'Gift cards', href: '/gift-cards' },
        { name: 'Promotional codes', href: '/help/promotional-codes' },
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Breadcrumb */}
      <div className="max-w-7xl mx-auto px-4 py-4">
        <nav className="text-sm text-gray-500">
          <Link href="/" className="hover:text-black">Home</Link>
          <span className="mx-2">/</span>
          <span className="text-black">Help Centre</span>
        </nav>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-12">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold mb-4">Help Centre</h1>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Find answers to your questions about shopping, delivery, returns, and more. 
            Our customer service team is here to help you with your luxury shopping experience.
          </p>
        </div>

        {/* Search */}
        <div className="max-w-2xl mx-auto mb-12">
          <div className="relative">
            <input
              type="text"
              placeholder="Search for help topics..."
              className="w-full px-6 py-4 border border-gray-300 focus:outline-none focus:border-black text-lg"
            />
            <button className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400">
              Search
            </button>
          </div>
        </div>

        {/* Help Categories */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {helpCategories.map((category, index) => (
            <div key={index} className="space-y-4">
              <h3 className="font-semibold text-lg">{category.title}</h3>
              <ul className="space-y-2">
                {category.links.map((link, linkIndex) => (
                  <li key={linkIndex}>
                    <Link
                      href={link.href}
                      className="text-gray-600 hover:text-black transition-colors text-sm"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Contact Section */}
        <div className="bg-gray-50 p-8 text-center">
          <h2 className="text-2xl font-bold mb-4">Still need help?</h2>
          <p className="text-gray-600 mb-6">
            Our customer service team is available to assist you with any questions.
          </p>
          <div className="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
            <button className="luxury-button">
              Live Chat
            </button>
            <button className="luxury-button-outline">
              Email Us
            </button>
          </div>
          <div className="mt-6 text-sm text-gray-500">
            <p>Customer Service Hours: Monday - Friday, 9AM - 6PM GMT</p>
          </div>
        </div>
      </div>
    </div>
  );
}
