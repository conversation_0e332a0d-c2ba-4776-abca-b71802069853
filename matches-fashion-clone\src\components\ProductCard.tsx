'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Heart, ShoppingBag } from 'lucide-react';

interface ProductCardProps {
  id: number;
  name: string;
  brand: string;
  price: number;
  originalPrice?: number;
  isOnSale?: boolean;
  image: string;
  category: string;
  href: string;
  className?: string;
}

const ProductCard = ({
  id,
  name,
  brand,
  price,
  originalPrice,
  isOnSale = false,
  category,
  href,
  className = ''
}: ProductCardProps) => {
  const [isWishlisted, setIsWishlisted] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const handleWishlistToggle = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsWishlisted(!isWishlisted);
  };

  const handleAddToBag = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    // Add to bag logic here
    console.log('Added to bag:', { id, name, brand, price });
  };

  return (
    <Link href={href} className={`group cursor-pointer block ${className}`}>
      <div 
        className="relative"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* Product Image */}
        <div className="aspect-[3/4] bg-gradient-to-br from-gray-100 to-gray-200 mb-4 overflow-hidden relative">
          <div className="w-full h-full flex items-center justify-center">
            <span className="text-gray-500 text-sm">Product Image</span>
          </div>
          
          {/* Sale Badge */}
          {isOnSale && (
            <div className="absolute top-3 left-3 bg-red-500 text-white text-xs px-2 py-1 uppercase tracking-wide">
              Sale
            </div>
          )}

          {/* Hover Actions */}
          {isHovered && (
            <div className="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center">
              <div className="flex space-x-3">
                <button
                  onClick={handleWishlistToggle}
                  className={`p-3 rounded-full transition-all duration-300 ${
                    isWishlisted 
                      ? 'bg-red-500 text-white' 
                      : 'bg-white text-black hover:bg-gray-100'
                  }`}
                  aria-label="Add to wishlist"
                >
                  <Heart size={20} fill={isWishlisted ? 'currentColor' : 'none'} />
                </button>
                <button
                  onClick={handleAddToBag}
                  className="p-3 bg-black text-white rounded-full hover:bg-gray-800 transition-all duration-300"
                  aria-label="Add to bag"
                >
                  <ShoppingBag size={20} />
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Product Info */}
        <div className="space-y-1">
          <p className="text-xs text-gray-500 uppercase tracking-wide">{brand}</p>
          <h3 className="text-sm font-medium group-hover:text-gray-600 transition-colors line-clamp-2">
            {name}
          </h3>
          <div className="flex items-center space-x-2">
            <span className="text-sm font-semibold">£{price}</span>
            {isOnSale && originalPrice && (
              <span className="text-sm text-gray-500 line-through">£{originalPrice}</span>
            )}
          </div>
          <p className="text-xs text-gray-500">{category}</p>
        </div>
      </div>
    </Link>
  );
};

export default ProductCard;
