'use client';

import Link from 'next/link';
import { Instagram, Facebook, Youtube, Twitter } from 'lucide-react';

const Footer = () => {
  return (
    <footer className="bg-white border-t border-gray-200">
      {/* Newsletter Section */}
      <div className="bg-gray-50 py-12">
        <div className="max-w-7xl mx-auto px-4 text-center">
          <h2 className="text-2xl font-bold mb-4">Stay in the know</h2>
          <p className="text-gray-600 mb-6 max-w-md mx-auto">
            Be the first to discover new arrivals, exclusive collections, and styling tips
          </p>
          <div className="flex max-w-md mx-auto">
            <input
              type="email"
              placeholder="Enter your email address"
              className="flex-1 px-4 py-3 border border-gray-300 focus:outline-none focus:border-black"
            />
            <button className="luxury-button">
              Sign up
            </button>
          </div>
        </div>
      </div>

      {/* Main Footer Content */}
      <div className="max-w-7xl mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8">
          {/* MATCHES */}
          <div className="lg:col-span-1">
            <h3 className="font-semibold text-sm uppercase tracking-wide mb-4">MATCHES</h3>
            <ul className="space-y-2">
              <li><Link href="/bio" className="text-sm text-gray-600 hover:text-black">About Us</Link></li>
              <li><Link href="/careers" className="text-sm text-gray-600 hover:text-black">Careers</Link></li>
              <li><Link href="/affiliates" className="text-sm text-gray-600 hover:text-black">Affiliates</Link></li>
              <li><Link href="/press" className="text-sm text-gray-600 hover:text-black">Press</Link></li>
            </ul>
          </div>

          {/* Services */}
          <div className="lg:col-span-1">
            <h3 className="font-semibold text-sm uppercase tracking-wide mb-4">Services</h3>
            <ul className="space-y-2">
              <li><Link href="/private-shopping" className="text-sm text-gray-600 hover:text-black">Private Shopping</Link></li>
              <li><Link href="/loyalty" className="text-sm text-gray-600 hover:text-black">Loyalty</Link></li>
              <li><Link href="/rental" className="text-sm text-gray-600 hover:text-black">MATCHES Rental</Link></li>
              <li><Link href="/gift-cards" className="text-sm text-gray-600 hover:text-black">Gift Cards</Link></li>
            </ul>
          </div>

          {/* Legal */}
          <div className="lg:col-span-1">
            <h3 className="font-semibold text-sm uppercase tracking-wide mb-4">Legal</h3>
            <ul className="space-y-2">
              <li><Link href="/terms" className="text-sm text-gray-600 hover:text-black">Terms and Conditions</Link></li>
              <li><Link href="/privacy" className="text-sm text-gray-600 hover:text-black">Privacy Policy</Link></li>
              <li><Link href="/cookies" className="text-sm text-gray-600 hover:text-black">Cookie Policy</Link></li>
              <li><Link href="/modern-slavery" className="text-sm text-gray-600 hover:text-black">Modern Slavery Statement</Link></li>
            </ul>
          </div>

          {/* Visit Us */}
          <div className="lg:col-span-1">
            <h3 className="font-semibold text-sm uppercase tracking-wide mb-4">Visit Us</h3>
            <ul className="space-y-2">
              <li><Link href="/stores/5carlosplace" className="text-sm text-gray-600 hover:text-black">5 Carlos Place</Link></li>
              <li><Link href="/stores/marylebone" className="text-sm text-gray-600 hover:text-black">Marylebone</Link></li>
              <li><Link href="/stores/wimbledon" className="text-sm text-gray-600 hover:text-black">Wimbledon</Link></li>
            </ul>
          </div>

          {/* Help Centre */}
          <div className="lg:col-span-1">
            <h3 className="font-semibold text-sm uppercase tracking-wide mb-4">Help Centre</h3>
            <ul className="space-y-2">
              <li><Link href="/help" className="text-sm text-gray-600 hover:text-black">Help Centre</Link></li>
              <li><Link href="/returns" className="text-sm text-gray-600 hover:text-black">Returning an item</Link></li>
              <li><Link href="/delivery" className="text-sm text-gray-600 hover:text-black">Delivery</Link></li>
              <li><Link href="/size-guide" className="text-sm text-gray-600 hover:text-black">Size Guide</Link></li>
            </ul>
          </div>

          {/* Social Media & Apps */}
          <div className="lg:col-span-1">
            <h3 className="font-semibold text-sm uppercase tracking-wide mb-4">Follow Us</h3>
            <div className="flex space-x-4 mb-6">
              <Link href="https://instagram.com/matches" className="text-gray-600 hover:text-black">
                <Instagram size={20} />
              </Link>
              <Link href="https://facebook.com/matches" className="text-gray-600 hover:text-black">
                <Facebook size={20} />
              </Link>
              <Link href="https://youtube.com/matches" className="text-gray-600 hover:text-black">
                <Youtube size={20} />
              </Link>
              <Link href="https://twitter.com/matches" className="text-gray-600 hover:text-black">
                <Twitter size={20} />
              </Link>
            </div>
            <h4 className="font-semibold text-sm uppercase tracking-wide mb-4">Our Apps</h4>
            <div className="space-y-2">
              <Link href="#" className="block">
                <div className="bg-black text-white px-3 py-2 text-xs rounded">
                  Download on the App Store
                </div>
              </Link>
              <Link href="#" className="block">
                <div className="bg-black text-white px-3 py-2 text-xs rounded">
                  Get it on Google Play
                </div>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-gray-200 py-6">
        <div className="max-w-7xl mx-auto px-4 flex flex-col md:flex-row justify-between items-center">
          <div className="flex items-center space-x-4 mb-4 md:mb-0">
            <span className="text-sm text-gray-600">Shipping to</span>
            <button className="text-sm font-medium border border-gray-300 px-3 py-1 hover:border-black">
              United Kingdom
            </button>
          </div>
          <div className="text-sm text-gray-600">
            © Copyright 2024 MATCHES
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
