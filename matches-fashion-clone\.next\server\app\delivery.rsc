1:"$Sreact.fragment"
2:I[8503,["874","static/chunks/874-4ab02debfaf779a6.js","177","static/chunks/app/layout-9b90e7ab0b316405.js"],"default"]
3:I[7555,[],""]
4:I[1295,[],""]
5:I[6874,["874","static/chunks/874-4ab02debfaf779a6.js","211","static/chunks/app/delivery/page-4eaa5b542306acd5.js"],""]
6:I[6987,["874","static/chunks/874-4ab02debfaf779a6.js","177","static/chunks/app/layout-9b90e7ab0b316405.js"],"default"]
7:I[2277,["874","static/chunks/874-4ab02debfaf779a6.js","177","static/chunks/app/layout-9b90e7ab0b316405.js"],"default"]
8:I[9665,[],"MetadataBoundary"]
a:I[9665,[],"OutletBoundary"]
d:I[4911,[],"AsyncMetadataOutlet"]
f:I[9665,[],"ViewportBoundary"]
11:I[6614,[],""]
:HL["/_next/static/css/1e19055866d1f255.css","style"]
0:{"P":null,"b":"GfHiHsXNIhYeWhao3WpiJ","p":"","c":["","delivery"],"i":false,"f":[[["",{"children":["delivery",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/1e19055866d1f255.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","children":["$","body",null,{"className":"min-h-screen flex flex-col antialiased","children":[["$","$L2",null,{}],["$","main",null,{"className":"flex-1","children":["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[["$","div",null,{"className":"min-h-screen flex items-center justify-center bg-white","children":["$","div",null,{"className":"text-center max-w-md mx-auto px-4","children":[["$","h1",null,{"className":"text-6xl font-bold mb-4","children":"404"}],["$","h2",null,{"className":"text-2xl font-semibold mb-4","children":"Page Not Found"}],["$","p",null,{"className":"text-gray-600 mb-8","children":"The page you are looking for might have been removed, had its name changed, or is temporarily unavailable."}],["$","div",null,{"className":"space-y-4","children":[["$","$L5",null,{"href":"/","className":"luxury-button block","children":"Return Home"}],["$","div",null,{"className":"flex justify-center space-x-4 text-sm","children":[["$","$L5",null,{"href":"/womens","className":"text-gray-600 hover:text-black","children":"Shop Women's"}],["$","$L5",null,{"href":"/mens","className":"text-gray-600 hover:text-black","children":"Shop Men's"}]]}]]}]]}]}],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}],["$","$L6",null,{}],["$","$L7",null,{}]]}]}]]}],{"children":["delivery",["$","$1","c",{"children":[null,["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","div",null,{"className":"min-h-screen bg-white","children":[["$","div",null,{"className":"max-w-7xl mx-auto px-4 py-4","children":["$","nav",null,{"className":"text-sm text-gray-500","children":[["$","$L5",null,{"href":"/","className":"hover:text-black","children":"Home"}],["$","span",null,{"className":"mx-2","children":"/"}],["$","span",null,{"className":"text-black","children":"Delivery"}]]}]}],["$","div",null,{"className":"max-w-4xl mx-auto px-4 py-12","children":[["$","h1",null,{"className":"text-4xl font-bold mb-8","children":"Delivery Information"}],["$","div",null,{"className":"space-y-8","children":[["$","section",null,{"children":[["$","h2",null,{"className":"text-2xl font-semibold mb-4","children":"UK Delivery"}],["$","div",null,{"className":"bg-gray-50 p-6 space-y-4","children":["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-2 gap-6","children":[["$","div",null,{"children":[["$","h3",null,{"className":"font-medium mb-2","children":"Standard Delivery"}],["$","p",null,{"className":"text-sm text-gray-600 mb-2","children":"3-5 working days"}],["$","p",null,{"className":"text-sm","children":"FREE on orders over £200"}],["$","p",null,{"className":"text-sm","children":"£5 on orders under £200"}]]}],["$","div",null,{"children":[["$","h3",null,{"className":"font-medium mb-2","children":"Express Delivery"}],["$","p",null,{"className":"text-sm text-gray-600 mb-2","children":"Next working day"}],["$","p",null,{"className":"text-sm","children":"£15 (order by 2pm)"}]]}]]}]}]]}],["$","section",null,{"children":[["$","h2",null,{"className":"text-2xl font-semibold mb-4","children":"International Delivery"}],["$","div",null,{"className":"bg-gray-50 p-6","children":[["$","p",null,{"className":"text-gray-600 mb-4","children":"We deliver to over 190 countries worldwide. Delivery times and costs vary by destination."}],["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm","children":[["$","div",null,{"children":[["$","h4",null,{"className":"font-medium","children":"Europe"}],["$","p",null,{"className":"text-gray-600","children":"3-7 working days"}],["$","p",null,{"children":"From £15"}]]}],["$","div",null,{"children":[["$","h4",null,{"className":"font-medium","children":"USA & Canada"}],["$","p",null,{"className":"text-gray-600","children":"5-10 working days"}],["$","p",null,{"children":"From £25"}]]}],["$","div",null,{"children":[["$","h4",null,{"className":"font-medium","children":"Rest of World"}],["$","p",null,{"className":"text-gray-600","children":"7-14 working days"}],["$","p",null,{"children":"From £35"}]]}]]}]]}]]}],["$","section",null,{"children":[["$","h2",null,{"className":"text-2xl font-semibold mb-4","children":"Order Processing"}],["$","div",null,{"className":"space-y-4","children":[["$","p",null,{"className":"text-gray-600","children":"Orders are processed Monday to Friday, excluding bank holidays. Orders placed after 2pm on Friday will be processed the following Monday."}],["$","ul",null,{"className":"list-disc list-inside space-y-2 text-gray-600","children":[["$","li",null,{"children":"You will receive an order confirmation email immediately after placing your order"}],["$","li",null,{"children":"A dispatch confirmation with tracking information will be sent when your order ships"}],["$","li",null,{"children":"Track your order using the link provided in your dispatch email"}]]}]]}]]}],["$","section",null,{"children":[["$","h2",null,{"className":"text-2xl font-semibold mb-4","children":"Special Delivery Services"}],["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-2 gap-6","children":[["$","div",null,{"className":"border border-gray-200 p-6","children":[["$","h3",null,{"className":"font-medium mb-3","children":"Saturday Delivery"}],["$","p",null,{"className":"text-sm text-gray-600 mb-2","children":"Available for UK orders"}],["$","p",null,{"className":"text-sm","children":"£25 additional charge"}]]}],["$","div",null,{"className":"border border-gray-200 p-6","children":[["$","h3",null,{"className":"font-medium mb-3","children":"Nominated Day Delivery"}],["$","p",null,{"className":"text-sm text-gray-600 mb-2","children":"Choose your delivery date"}],["$","p",null,{"className":"text-sm","children":"£20 additional charge"}]]}]]}]]}],["$","section",null,{"className":"bg-gray-50 p-6 text-center","children":[["$","h3",null,{"className":"font-semibold mb-2","children":"Need help with your delivery?"}],["$","p",null,{"className":"text-gray-600 mb-4","children":"Contact our customer service team for assistance"}],["$","$L5",null,{"href":"/help","className":"luxury-button-outline","children":"Contact Us"}]]}]]}]]}]]}],["$","$L8",null,{"children":"$L9"}],null,["$","$La",null,{"children":["$Lb","$Lc",["$","$Ld",null,{"promise":"$@e"}]]}]]}],{},null,false]},null,false]},null,false],["$","$1","h",{"children":[null,["$","$1","TwriONF376RBDPAmq0eBn",{"children":[["$","$Lf",null,{"children":"$L10"}],null]}],null]}],false]],"m":"$undefined","G":["$11","$undefined"],"s":false,"S":true}
12:"$Sreact.suspense"
13:I[4911,[],"AsyncMetadata"]
9:["$","$12",null,{"fallback":null,"children":["$","$L13",null,{"promise":"$@14"}]}]
c:null
10:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
b:null
14:{"metadata":[["$","title","0",{"children":"Luxury Fashion | Designer Clothing, Bags & Shoes | MATCHES UK"}],["$","meta","1",{"name":"description","content":"Discover luxury fashion at MATCHES. Shop designer clothing, bags, shoes and accessories from over 450 established and innovative designer brands."}],["$","meta","2",{"name":"keywords","content":"luxury fashion, designer clothing, designer bags, designer shoes, luxury accessories, high-end fashion"}],["$","link","3",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}]],"error":null,"digest":"$undefined"}
e:{"metadata":"$14:metadata","error":null,"digest":"$undefined"}
