exports.id=165,exports.ids=[165],exports.modules={1159:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\matches-fashion-clone\\\\src\\\\components\\\\ProductCarousel.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\matches-headless-ui\\matches-fashion-clone\\src\\components\\ProductCarousel.tsx","default")},2707:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,4536,23)),Promise.resolve().then(s.bind(s,1159))},5913:(e,t,s)=>{"use strict";s(6397);var r=s(3210),n=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(r),i="undefined"!=typeof process&&process.env&&!0,o=function(e){return"[object String]"===Object.prototype.toString.call(e)},c=function(){function e(e){var t=void 0===e?{}:e,s=t.name,r=void 0===s?"stylesheet":s,n=t.optimizeForSpeed,c=void 0===n?i:n;l(o(r),"`name` must be a string"),this._name=r,this._deletedRulePlaceholder="#"+r+"-deleted-rule____{}",l("boolean"==typeof c,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=c,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0,this._nonce=null}var t,s=e.prototype;return s.setOptimizeForSpeed=function(e){l("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),l(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},s.isOptimizeForSpeed=function(){return this._optimizeForSpeed},s.inject=function(){var e=this;l(!this._injected,"sheet already injected"),this._injected=!0,this._serverSheet={cssRules:[],insertRule:function(t,s){return"number"==typeof s?e._serverSheet.cssRules[s]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),s},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},s.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},s.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},s.insertRule=function(e,t){return l(o(e),"`insertRule` accepts only strings"),"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++},s.replaceRule=function(e,t){this._optimizeForSpeed;var s=this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!s.cssRules[e])return e;s.deleteRule(e);try{s.insertRule(t,e)}catch(r){i||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),s.insertRule(this._deletedRulePlaceholder,e)}return e},s.deleteRule=function(e){this._serverSheet.deleteRule(e)},s.flush=function(){this._injected=!1,this._rulesCount=0,this._serverSheet.cssRules=[]},s.cssRules=function(){return this._serverSheet.cssRules},s.makeStyleTag=function(e,t,s){t&&l(o(t),"makeStyleTag accepts only strings as second parameter");var r=document.createElement("style");this._nonce&&r.setAttribute("nonce",this._nonce),r.type="text/css",r.setAttribute("data-"+e,""),t&&r.appendChild(document.createTextNode(t));var n=document.head||document.getElementsByTagName("head")[0];return s?n.insertBefore(r,s):n.appendChild(r),r},t=[{key:"length",get:function(){return this._rulesCount}}],function(e,t){for(var s=0;s<t.length;s++){var r=t[s];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}(e.prototype,t),e}();function l(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var a=function(e){for(var t=5381,s=e.length;s;)t=33*t^e.charCodeAt(--s);return t>>>0},d={};function u(e,t){if(!t)return"jsx-"+e;var s=String(t),r=e+s;return d[r]||(d[r]="jsx-"+a(e+"-"+s)),d[r]}function h(e,t){var s=e+(t=t.replace(/\/style/gi,"\\/style"));return d[s]||(d[s]=t.replace(/__jsx-style-dynamic-selector/g,e)),d[s]}var f=function(){function e(e){var t=void 0===e?{}:e,s=t.styleSheet,r=void 0===s?null:s,n=t.optimizeForSpeed,i=void 0!==n&&n;this._sheet=r||new c({name:"styled-jsx",optimizeForSpeed:i}),this._sheet.inject(),r&&"boolean"==typeof i&&(this._sheet.setOptimizeForSpeed(i),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed());var s=this.getIdAndRules(e),r=s.styleId,n=s.rules;if(r in this._instancesCounts){this._instancesCounts[r]+=1;return}var i=n.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[r]=i,this._instancesCounts[r]=1},t.remove=function(e){var t=this,s=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(s in this._instancesCounts,"styleId: `"+s+"` not found"),this._instancesCounts[s]-=1,this._instancesCounts[s]<1){var r=this._fromServer&&this._fromServer[s];r?(r.parentNode.removeChild(r),delete this._fromServer[s]):(this._indices[s].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[s]),delete this._instancesCounts[s]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],s=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return s[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,s;return t=this.cssRules(),void 0===(s=e)&&(s={}),t.map(function(e){var t=e[0],r=e[1];return n.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:s.nonce?s.nonce:void 0,dangerouslySetInnerHTML:{__html:r}})})},t.getIdAndRules=function(e){var t=e.children,s=e.dynamic,r=e.id;if(s){var n=u(r,s);return{styleId:n,rules:Array.isArray(t)?t.map(function(e){return h(n,e)}):[h(n,t)]}}return{styleId:u(r),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),m=r.createContext(null);m.displayName="StyleSheetContext";n.default.useInsertionEffect||n.default.useLayoutEffect;var p=void 0;function x(e){var t=p||r.useContext(m);return t&&t.add(e),null}x.dynamic=function(e){return e.map(function(e){return u(e[0],e[1])}).join(" ")},t.style=x},6064:(e,t,s)=>{"use strict";s.d(t,{default:()=>h});var r=s(687),n=s(6180),i=s.n(n),o=s(3210),c=s(5814),l=s.n(c),a=s(2688);let d=(0,a.A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]),u=(0,a.A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]),h=({title:e,subtitle:t,count:s,ctaText:n="Shop Now",ctaHref:c,products:a})=>{let[h,f]=(0,o.useState)(!1),[m,p]=(0,o.useState)(!0),x=(0,o.useRef)(null),v=()=>{if(x.current){let{scrollLeft:e,scrollWidth:t,clientWidth:s}=x.current;f(e>0),p(e<t-s-1)}};(0,o.useEffect)(()=>{v();let e=x.current;if(e)return e.addEventListener("scroll",v),()=>e.removeEventListener("scroll",v)},[]);let b=e=>{if(x.current){let t=x.current.scrollLeft+("left"===e?-300:300);x.current.scrollTo({left:t,behavior:"smooth"})}};return(0,r.jsxs)("section",{className:"jsx-d1e1d09fdfc6c76b homepage-component component-product-carousel relative bg-white py-4",children:[(0,r.jsxs)("div",{className:"jsx-d1e1d09fdfc6c76b product-carousel-wrapper relative",children:[(0,r.jsx)("button",{onClick:()=>b("left"),disabled:!h,"aria-label":"Previous products",className:`jsx-d1e1d09fdfc6c76b product-carousel-arrow arrow-prev absolute left-2 top-1/2 transform -translate-y-1/2 z-10 w-10 h-10 bg-white border border-gray-300 hover:border-black transition-colors ${!h?"opacity-50 cursor-not-allowed":"cursor-pointer"}`,children:(0,r.jsx)(d,{size:16,className:"mx-auto"})}),(0,r.jsx)("div",{ref:x,style:{scrollbarWidth:"none",msOverflowStyle:"none"},className:"jsx-d1e1d09fdfc6c76b product-carousel-scroller overflow-x-auto hide-scrollbar px-12",children:(0,r.jsxs)("div",{className:"jsx-d1e1d09fdfc6c76b product-carousel-items flex space-x-3 min-w-max",children:[(0,r.jsx)("div",{className:"jsx-d1e1d09fdfc6c76b item-card item-wide flex-shrink-0 w-72 h-80 bg-gray-50 flex flex-col justify-center items-center text-center p-6",children:(0,r.jsxs)(l(),{href:c,className:"item-info block",children:[(0,r.jsxs)("h3",{className:"jsx-d1e1d09fdfc6c76b mb-4",children:[(0,r.jsx)("span",{className:"jsx-d1e1d09fdfc6c76b homepage-skeleton number block text-3xl font-bold mb-1",children:s}),(0,r.jsx)("span",{className:"jsx-d1e1d09fdfc6c76b homepage-skeleton title block text-sm font-medium uppercase tracking-wide mb-1",children:e}),(0,r.jsx)("span",{className:"jsx-d1e1d09fdfc6c76b homepage-skeleton subtitle block text-xs text-gray-600 uppercase tracking-wide",children:t})]}),(0,r.jsxs)("p",{className:"jsx-d1e1d09fdfc6c76b cta homepage-skeleton flex items-center justify-center space-x-1 text-xs font-medium uppercase tracking-wide hover:text-gray-600 transition-colors",children:[(0,r.jsx)("span",{className:"jsx-d1e1d09fdfc6c76b",children:n}),(0,r.jsx)("svg",{height:"6",viewBox:"0 0 4 8",width:"3",xmlns:"http://www.w3.org/2000/svg",className:"jsx-d1e1d09fdfc6c76b cta-arrow",children:(0,r.jsx)("path",{d:"m0 0 4 4-4 4z",fill:"currentColor",className:"jsx-d1e1d09fdfc6c76b"})})]})]})}),a.map(e=>(0,r.jsx)(l(),{href:e.href,className:"item-card product-carousel-item flex-shrink-0 w-48 h-80 group cursor-pointer","aria-label":`${e.designer}. ${e.name}`,children:(0,r.jsxs)("figure",{className:"jsx-d1e1d09fdfc6c76b h-full",children:[(0,r.jsx)("div",{className:"jsx-d1e1d09fdfc6c76b image-wrapper h-5/6 bg-gradient-to-br from-gray-100 to-gray-200 overflow-hidden group-hover:opacity-90 transition-opacity",children:(0,r.jsx)("div",{className:"jsx-d1e1d09fdfc6c76b w-full h-full flex items-center justify-center",children:(0,r.jsx)("span",{className:"jsx-d1e1d09fdfc6c76b text-gray-500 text-xs",children:"Product Image"})})}),(0,r.jsx)("figcaption",{className:"jsx-d1e1d09fdfc6c76b designer h-1/6 flex items-center justify-center text-xs font-medium text-gray-700 group-hover:text-black transition-colors",children:e.designer})]})},e.id)),(0,r.jsxs)(l(),{href:c,className:"item-card product-carousel-item item-discover flex-shrink-0 w-48 h-80 bg-black text-white flex flex-col justify-center items-center text-center p-6 hover:bg-gray-800 transition-colors",children:[(0,r.jsx)("h3",{className:"jsx-d1e1d09fdfc6c76b homepage-skeleton discover-text text-sm font-medium mb-3",children:"Discover the latest arrivals"}),(0,r.jsxs)("p",{className:"jsx-d1e1d09fdfc6c76b cta homepage-skeleton flex items-center space-x-1 text-xs font-medium uppercase tracking-wide",children:[(0,r.jsx)("span",{className:"jsx-d1e1d09fdfc6c76b",children:n}),(0,r.jsx)("svg",{height:"6",viewBox:"0 0 4 8",width:"3",xmlns:"http://www.w3.org/2000/svg",className:"jsx-d1e1d09fdfc6c76b cta-arrow",children:(0,r.jsx)("path",{d:"m0 0 4 4-4 4z",fill:"currentColor",className:"jsx-d1e1d09fdfc6c76b"})})]})]})]})}),(0,r.jsx)("button",{onClick:()=>b("right"),disabled:!m,"aria-label":"Next products",className:`jsx-d1e1d09fdfc6c76b product-carousel-arrow arrow-next absolute right-2 top-1/2 transform -translate-y-1/2 z-10 w-10 h-10 bg-white border border-gray-300 hover:border-black transition-colors ${!m?"opacity-50 cursor-not-allowed":"cursor-pointer"}`,children:(0,r.jsx)(u,{size:16,className:"mx-auto"})})]}),(0,r.jsx)(i(),{id:"d1e1d09fdfc6c76b",children:".hide-scrollbar.jsx-d1e1d09fdfc6c76b::-webkit-scrollbar{display:none}"})]})}},6180:(e,t,s)=>{"use strict";e.exports=s(5913).style},6397:()=>{},9659:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,5814,23)),Promise.resolve().then(s.bind(s,6064))}};