{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/matches-headless-ui/matches-fashion-clone/src/components/ProductCarousel.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ProductCarousel.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ProductCarousel.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsS,GACnU,oEACA", "debugId": null}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/matches-headless-ui/matches-fashion-clone/src/components/ProductCarousel.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ProductCarousel.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ProductCarousel.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkR,GAC/S,gDACA", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/matches-headless-ui/matches-fashion-clone/src/app/page.tsx"], "sourcesContent": ["import Link from \"next/link\";\nimport ProductCarousel from \"@/components/ProductCarousel\";\n\nexport default function Home() {\n  // Mock product data for carousel\n  const justInProducts = [\n    { id: '1', name: 'Oversized striped cashmere rugby shirt', designer: '<PERSON><PERSON>', image: '', href: '/womens/product/1' },\n    { id: '2', name: 'T-Lock small grained-leather cross-body bag', designer: 'Toteme', image: '', href: '/womens/product/2' },\n    { id: '3', name: 'Duomo pleated linen straight-leg trousers', designer: 'Faithfull The Brand', image: '', href: '/womens/product/3' },\n    { id: '4', name: 'Ava leather Mary Jane flats', designer: 'The Row', image: '', href: '/womens/product/4' },\n    { id: '5', name: 'Anagram-logo small leather-trim raffia basket bag', designer: 'LOEWE', image: '', href: '/womens/product/5' },\n    { id: '6', name: 'Scoop-neck organic cotton-blend tank top', designer: 'Toteme', image: '', href: '/womens/product/6' },\n    { id: '7', name: 'The Asymmetric leather ballet flats', designer: 'Toteme', image: '', href: '/womens/product/7' },\n    { id: '8', name: 'Patch-pocket cotton blend-twill trousers', designer: 'Frame', image: '', href: '/womens/product/8' },\n    { id: '9', name: 'Anagram-logo leather-trim raffia basket bag', designer: 'LOEWE', image: '', href: '/womens/product/9' },\n    { id: '10', name: 'Hinged 18kt gold-vermeil hoop earrings', designer: 'Sophie Buhai', image: '', href: '/womens/product/10' },\n    { id: '11', name: 'Puff-sleeve pleated midi dress', designer: 'Zimmermann', image: '', href: '/womens/product/11' },\n    { id: '12', name: 'Rockstud 60 leather block-heel sandals', designer: 'Valentino Garavani', image: '', href: '/womens/product/12' },\n  ];\n\n  return (\n    <div className=\"bg-white\">\n      {/* Hero Section - Large Image Block */}\n      <section className=\"relative\">\n        <div className=\"aspect-[16/9] lg:aspect-[21/9] bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center\">\n          <div className=\"w-full h-full flex items-center justify-center\">\n            <span className=\"text-gray-500 text-lg\">Hero Image</span>\n          </div>\n        </div>\n        <div className=\"absolute inset-0 flex items-center justify-center\">\n          <div className=\"text-center text-black\">\n            <p className=\"text-sm uppercase tracking-wide mb-2\">24/7 STYLE</p>\n            <h1 className=\"text-4xl md:text-6xl font-bold mb-4\">SHOP</h1>\n          </div>\n        </div>\n      </section>\n\n      {/* Product Carousel - Just In */}\n      <ProductCarousel\n        title=\"NEW STYLES\"\n        subtitle=\"JUST LANDED\"\n        count={545}\n        ctaText=\"Shop Now\"\n        ctaHref=\"/womens/just-in/just-in-this-month\"\n        products={justInProducts}\n        type=\"just-in\"\n      />\n\n      {/* Main Content Grid - Exact Layout Match */}\n      <section className=\"max-w-7xl mx-auto px-4 py-8\">\n        {/* First Row - Two Large Blocks (4:5 aspect ratio) */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4\">\n          {/* Left Block - Editorial Content */}\n          <Link href=\"/womens/stories\" className=\"group relative overflow-hidden\">\n            <div className=\"aspect-[4/5] bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center\">\n              <span className=\"text-gray-500\">Editorial Image</span>\n            </div>\n            <div className=\"absolute bottom-0 left-0 right-0 bg-white bg-opacity-90 p-4\">\n              <h3 className=\"text-sm font-medium uppercase tracking-wide\">STYLE INSPIRATION</h3>\n              <p className=\"text-xs text-gray-600 mt-1\">Discover the latest trends</p>\n            </div>\n          </Link>\n\n          {/* Right Block - Featured Product */}\n          <Link href=\"/womens/shop/shoes\" className=\"group relative overflow-hidden\">\n            <div className=\"aspect-[4/5] bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center\">\n              <span className=\"text-gray-500\">Product Image</span>\n            </div>\n            <div className=\"absolute bottom-0 left-0 right-0 bg-white bg-opacity-90 p-4\">\n              <h3 className=\"text-sm font-medium uppercase tracking-wide\">THE EDITORS</h3>\n              <p className=\"text-xs text-gray-600 mt-1\">Curated selections</p>\n            </div>\n          </Link>\n        </div>\n\n        {/* Second Row - Three Medium Blocks (3:4 aspect ratio) */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\">\n          <Link href=\"/womens/shop/clothing/dresses\" className=\"group relative overflow-hidden\">\n            <div className=\"aspect-[3/4] bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center\">\n              <span className=\"text-gray-500\">Dress Image</span>\n            </div>\n            <div className=\"absolute bottom-0 left-0 right-0 bg-white bg-opacity-90 p-3\">\n              <h3 className=\"text-xs font-medium uppercase tracking-wide\">DRESSES</h3>\n            </div>\n          </Link>\n\n          <Link href=\"/womens/shop/bags\" className=\"group relative overflow-hidden\">\n            <div className=\"aspect-[3/4] bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center\">\n              <span className=\"text-gray-500\">Bag Image</span>\n            </div>\n            <div className=\"absolute bottom-0 left-0 right-0 bg-white bg-opacity-90 p-3\">\n              <h3 className=\"text-xs font-medium uppercase tracking-wide\">BAGS</h3>\n            </div>\n          </Link>\n\n          <Link href=\"/womens/shop/clothing/knitwear\" className=\"group relative overflow-hidden\">\n            <div className=\"aspect-[3/4] bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center\">\n              <span className=\"text-gray-500\">Knitwear Image</span>\n            </div>\n            <div className=\"absolute bottom-0 left-0 right-0 bg-white bg-opacity-90 p-3\">\n              <h3 className=\"text-xs font-medium uppercase tracking-wide\">KNITWEAR</h3>\n            </div>\n          </Link>\n        </div>\n\n        {/* Third Row - Two Large Blocks (4:5 aspect ratio) */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-4\">\n          <Link href=\"/womens/designers/the-row\" className=\"group relative overflow-hidden\">\n            <div className=\"aspect-[4/5] bg-gradient-to-br from-gray-200 to-gray-400 flex items-center justify-center\">\n              <span className=\"text-gray-500\">Designer Feature</span>\n            </div>\n            <div className=\"absolute bottom-0 left-0 right-0 bg-white bg-opacity-90 p-4\">\n              <h3 className=\"text-sm font-medium uppercase tracking-wide\">DESIGNER SPOTLIGHT</h3>\n              <p className=\"text-xs text-gray-600 mt-1\">Exclusive collections</p>\n            </div>\n          </Link>\n\n          <Link href=\"/womens/just-in\" className=\"group relative overflow-hidden\">\n            <div className=\"aspect-[4/5] bg-gradient-to-br from-gray-300 to-gray-500 flex items-center justify-center\">\n              <span className=\"text-gray-500\">New Arrivals</span>\n            </div>\n            <div className=\"absolute bottom-0 left-0 right-0 bg-white bg-opacity-90 p-4\">\n              <h3 className=\"text-sm font-medium uppercase tracking-wide\">NEW ARRIVALS</h3>\n              <p className=\"text-xs text-gray-600 mt-1\">Just landed</p>\n            </div>\n          </Link>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS;IACtB,iCAAiC;IACjC,MAAM,iBAAiB;QACrB;YAAE,IAAI;YAAK,MAAM;YAA0C,UAAU;YAAQ,OAAO;YAAI,MAAM;QAAoB;QAClH;YAAE,IAAI;YAAK,MAAM;YAA+C,UAAU;YAAU,OAAO;YAAI,MAAM;QAAoB;QACzH;YAAE,IAAI;YAAK,MAAM;YAA6C,UAAU;YAAuB,OAAO;YAAI,MAAM;QAAoB;QACpI;YAAE,IAAI;YAAK,MAAM;YAA+B,UAAU;YAAW,OAAO;YAAI,MAAM;QAAoB;QAC1G;YAAE,IAAI;YAAK,MAAM;YAAqD,UAAU;YAAS,OAAO;YAAI,MAAM;QAAoB;QAC9H;YAAE,IAAI;YAAK,MAAM;YAA4C,UAAU;YAAU,OAAO;YAAI,MAAM;QAAoB;QACtH;YAAE,IAAI;YAAK,MAAM;YAAuC,UAAU;YAAU,OAAO;YAAI,MAAM;QAAoB;QACjH;YAAE,IAAI;YAAK,MAAM;YAA4C,UAAU;YAAS,OAAO;YAAI,MAAM;QAAoB;QACrH;YAAE,IAAI;YAAK,MAAM;YAA+C,UAAU;YAAS,OAAO;YAAI,MAAM;QAAoB;QACxH;YAAE,IAAI;YAAM,MAAM;YAA0C,UAAU;YAAgB,OAAO;YAAI,MAAM;QAAqB;QAC5H;YAAE,IAAI;YAAM,MAAM;YAAkC,UAAU;YAAc,OAAO;YAAI,MAAM;QAAqB;QAClH;YAAE,IAAI;YAAM,MAAM;YAA0C,UAAU;YAAsB,OAAO;YAAI,MAAM;QAAqB;KACnI;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;0CAAwB;;;;;;;;;;;;;;;;kCAG5C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAuC;;;;;;8CACpD,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;;;;;;;;;;;;;;;;;;0BAM1D,8OAAC,qIAAA,CAAA,UAAe;gBACd,OAAM;gBACN,UAAS;gBACT,OAAO;gBACP,SAAQ;gBACR,SAAQ;gBACR,UAAU;gBACV,MAAK;;;;;;0BAIP,8OAAC;gBAAQ,WAAU;;kCAEjB,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAkB,WAAU;;kDACrC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;kDAElC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA8C;;;;;;0DAC5D,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;;;;;;;0CAK9C,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAqB,WAAU;;kDACxC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;kDAElC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA8C;;;;;;0DAC5D,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;;;;;;;;;;;;;kCAMhD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAgC,WAAU;;kDACnD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;kDAElC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAG,WAAU;sDAA8C;;;;;;;;;;;;;;;;;0CAIhE,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAoB,WAAU;;kDACvC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;kDAElC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAG,WAAU;sDAA8C;;;;;;;;;;;;;;;;;0CAIhE,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAiC,WAAU;;kDACpD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;kDAElC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAG,WAAU;sDAA8C;;;;;;;;;;;;;;;;;;;;;;;kCAMlE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAA4B,WAAU;;kDAC/C,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;kDAElC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA8C;;;;;;0DAC5D,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;;;;;;;0CAI9C,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAkB,WAAU;;kDACrC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;kDAElC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA8C;;;;;;0DAC5D,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxD", "debugId": null}}, {"offset": {"line": 635, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/matches-headless-ui/matches-fashion-clone/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 673, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/matches-headless-ui/matches-fashion-clone/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,CAA8C,EAAtB,AAAuB;YAAA;gBAEzG,UAAA,CAAA;gBAAA,QAAA;oBAAA,IAAA,0BAA4D;oBAAA;iBAAA;YAC5D;SAAA,KAAO,MAAMC,cAAc,IAAIX,mBAAmB;;KAChDY,YAAY;cACVC,IAAAA,EAAMZ;YAAAA,MAAAA,CAAUa,QAAQ;iBACxBC,MAAM,QAAA;wBAAA;4BACNC,KAAAA,CAAAA,GAAAA,4MAAAA,CAAAA,KAAU,iBAAA,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACV,OAAA,GAAA,6SAAA,CAAA,UAAA,CAAA,KAAA,CAA2C,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BAC3CC,MAAAA,CAAAA,KAAY,OAAA,CAAA;;qBACZC,UAAU;gBACVC,UAAU,EAAE;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;UACAC,UAAU,CAAA;YAAA,IAAA;YAAA;SAAA;cACRC,OAAAA;YAAAA,IAAYnB;YAAAA;SAAAA;UACd,cAAA;YAAA,IAAA;YAAA;SAAA;IACF;CAAA,CAAE", "ignoreList": [0], "debugId": null}}]}