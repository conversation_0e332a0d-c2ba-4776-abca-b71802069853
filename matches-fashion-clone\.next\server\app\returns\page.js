"use strict";(()=>{var e={};e.id=762,e.ids=[762],e.modules={846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2737:(e,s,r)=>{r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>h,pages:()=>o,routeModule:()=>m,tree:()=>d});var a=r(5239),t=r(8088),i=r(8170),n=r.n(i),l=r(893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);r.d(s,c);let d={children:["",{children:["returns",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,4272)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\matches-headless-ui\\matches-fashion-clone\\src\\app\\returns\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\matches-headless-ui\\matches-fashion-clone\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,4413)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\matches-headless-ui\\matches-fashion-clone\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\matches-headless-ui\\matches-fashion-clone\\src\\app\\returns\\page.tsx"],h={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/returns/page",pathname:"/returns",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},3033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{e.exports=require("path")},4272:(e,s,r)=>{r.r(s),r.d(s,{default:()=>n});var a=r(7413),t=r(4536),i=r.n(t);function n(){return(0,a.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 py-4",children:(0,a.jsxs)("nav",{className:"text-sm text-gray-500",children:[(0,a.jsx)(i(),{href:"/",className:"hover:text-black",children:"Home"}),(0,a.jsx)("span",{className:"mx-2",children:"/"}),(0,a.jsx)("span",{className:"text-black",children:"Returns"})]})}),(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 py-12",children:[(0,a.jsx)("h1",{className:"text-4xl font-bold mb-8",children:"Returns & Exchanges"}),(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("section",{children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"Return Policy"}),(0,a.jsxs)("div",{className:"bg-gray-50 p-6 space-y-4",children:[(0,a.jsx)("p",{className:"text-gray-600",children:"We want you to be completely satisfied with your purchase. If you're not happy with your order, you can return it within 28 days of delivery for a full refund."}),(0,a.jsxs)("ul",{className:"list-disc list-inside space-y-2 text-gray-600",children:[(0,a.jsx)("li",{children:"Items must be in original condition with all tags attached"}),(0,a.jsx)("li",{children:"Items must be unworn and in original packaging"}),(0,a.jsx)("li",{children:"Personalized or made-to-order items cannot be returned"}),(0,a.jsx)("li",{children:"Underwear, swimwear, and pierced jewelry cannot be returned for hygiene reasons"})]})]})]}),(0,a.jsxs)("section",{children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"How to Return"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium mb-3",children:"UK Returns"}),(0,a.jsxs)("ol",{className:"list-decimal list-inside space-y-2 text-sm text-gray-600",children:[(0,a.jsx)("li",{children:"Log into your account and select the items to return"}),(0,a.jsx)("li",{children:"Print your prepaid return label"}),(0,a.jsx)("li",{children:"Package items securely in original packaging"}),(0,a.jsx)("li",{children:"Attach the return label and drop off at any Royal Mail post office"})]}),(0,a.jsx)("p",{className:"text-sm font-medium mt-3",children:"FREE returns for UK customers"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium mb-3",children:"International Returns"}),(0,a.jsxs)("ol",{className:"list-decimal list-inside space-y-2 text-sm text-gray-600",children:[(0,a.jsx)("li",{children:"Contact our customer service team"}),(0,a.jsx)("li",{children:"We'll provide return instructions and label"}),(0,a.jsx)("li",{children:"Package items securely"}),(0,a.jsx)("li",{children:"Send via your local postal service"})]}),(0,a.jsx)("p",{className:"text-sm font-medium mt-3",children:"Return shipping costs apply"})]})]})]}),(0,a.jsxs)("section",{children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"Exchanges"}),(0,a.jsxs)("div",{className:"bg-gray-50 p-6",children:[(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"We don't offer direct exchanges. To exchange an item, please return your original purchase and place a new order for the item you want."}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"This ensures you receive your new item as quickly as possible and aren't charged twice."})]})]}),(0,a.jsxs)("section",{children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"Refunds"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("p",{className:"text-gray-600",children:"Once we receive your return, we'll inspect the items and process your refund within 5-10 working days."}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium mb-2",children:"Refund Methods"}),(0,a.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[(0,a.jsx)("li",{children:"• Original payment method"}),(0,a.jsx)("li",{children:"• Store credit (if preferred)"}),(0,a.jsx)("li",{children:"• Gift card (for gift purchases)"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium mb-2",children:"Processing Times"}),(0,a.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[(0,a.jsx)("li",{children:"• Credit/Debit cards: 3-5 working days"}),(0,a.jsx)("li",{children:"• PayPal: 1-2 working days"}),(0,a.jsx)("li",{children:"• Bank transfer: 5-7 working days"})]})]})]})]})]}),(0,a.jsxs)("section",{children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"Damaged or Faulty Items"}),(0,a.jsxs)("div",{className:"bg-red-50 border border-red-200 p-6",children:[(0,a.jsx)("p",{className:"text-gray-700 mb-4",children:"If you receive a damaged or faulty item, please contact us immediately. We'll arrange for a replacement or full refund, including return shipping costs."}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Please take photos of any damage and include them when contacting our customer service team."})]})]}),(0,a.jsxs)("section",{className:"bg-gray-50 p-6 text-center",children:[(0,a.jsx)("h3",{className:"font-semibold mb-2",children:"Need help with a return?"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Our customer service team is here to help"}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4",children:[(0,a.jsx)(i(),{href:"/help",className:"luxury-button-outline",children:"Contact Us"}),(0,a.jsx)("button",{className:"luxury-button",children:"Start Return"})]})]})]})]})]})}},9121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{e.exports=require("url")}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),a=s.X(0,[447,304,430],()=>r(2737));module.exports=a})();