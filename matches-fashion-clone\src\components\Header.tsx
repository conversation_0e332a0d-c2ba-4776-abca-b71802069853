'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Menu, X, Search, User, Settings, Heart, ShoppingBag } from 'lucide-react';
import MegaMenu from './MegaMenu';
import SearchModal from './SearchModal';

const Header = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [activeMenu, setActiveMenu] = useState<string | null>(null);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [hoverTimeout, setHoverTimeout] = useState<NodeJS.Timeout | null>(null);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const handleMenuHover = (menu: string) => {
    if (hoverTimeout) {
      clearTimeout(hoverTimeout);
      setHoverTimeout(null);
    }
    setActiveMenu(menu);
  };

  const handleMenuLeave = () => {
    const timeout = setTimeout(() => {
      setActiveMenu(null);
    }, 300); // Longer delay to allow moving to mega menu
    setHoverTimeout(timeout);
  };

  return (
    <header className="relative bg-white border-b border-gray-200">
      {/* Top Bar - Hidden on mobile */}
      <div className="hidden md:block bg-black text-white text-xs py-2">
        <div className="max-w-7xl mx-auto px-4 flex justify-between items-center">
          <div className="flex space-x-6">
            <Link href="/help" className="hover:underline">Help Centre</Link>
            <Link href="/delivery" className="hover:underline">Delivery</Link>
            <Link href="/returns" className="hover:underline">Returns</Link>
          </div>
          <div className="flex space-x-6">
            <Link href="/stores" className="hover:underline">Visit Us</Link>
            <Link href="/apps" className="hover:underline">Our Apps</Link>
          </div>
        </div>
      </div>

      {/* Main Header */}
      <div
        className="max-w-7xl mx-auto px-4 relative"
        onMouseLeave={handleMenuLeave}
      >
        {/* First Line: Logo - Men - Women - Search Bar */}
        <div className="flex items-center justify-between h-16 md:h-20">
          {/* Mobile Menu Button */}
          <button
            className="flex md:hidden p-2 mr-4"
            onClick={toggleMobileMenu}
            aria-label="Toggle menu"
          >
            {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>

          {/* Left Side: Logo + Primary Navigation */}
          <div className="flex items-center">
            {/* Logo */}
            <Link href="/" className="flex-shrink-0 mr-8">
              <span className="text-2xl md:text-3xl font-bold tracking-wider">MATCHES</span>
            </Link>

            {/* Primary Categories - Men & Women */}
            <nav className="hidden md:flex items-center space-x-8">
              <div
                onMouseEnter={() => handleMenuHover('men')}
              >
                <Link
                  href="/mens"
                  className="text-sm font-medium tracking-wide uppercase hover:text-gray-600 transition-colors"
                >
                  Men
                </Link>
              </div>
              <div
                onMouseEnter={() => handleMenuHover('women')}
              >
                <Link
                  href="/womens"
                  className="text-sm font-medium tracking-wide uppercase hover:text-gray-600 transition-colors"
                >
                  Women
                </Link>
              </div>
            </nav>
          </div>

          {/* Right Side: Search Bar + Icons */}
          <div className="flex items-center space-x-4">
            {/* Search Bar */}
            <div className="hidden md:flex items-center bg-gray-100 rounded-full px-4 py-2 w-80">
              <Search size={16} className="text-gray-400 mr-2" />
              <input
                type="text"
                placeholder="Search for products, designers..."
                className="bg-transparent text-sm flex-1 outline-none placeholder-gray-500"
                onClick={() => setIsSearchOpen(true)}
                readOnly
              />
            </div>

            {/* Mobile Search Button */}
            <button
              className="flex md:hidden p-2 hover:bg-gray-100 rounded-full transition-colors"
              onClick={() => setIsSearchOpen(true)}
            >
              <Search size={20} />
            </button>

            {/* Right Side Icons */}
            <button className="p-2 hover:bg-gray-100 rounded-full transition-colors">
              <User size={20} />
            </button>
            <button className="p-2 hover:bg-gray-100 rounded-full transition-colors">
              <Settings size={20} />
            </button>
            <button className="p-2 hover:bg-gray-100 rounded-full transition-colors">
              <Heart size={20} />
            </button>
            <button className="p-2 hover:bg-gray-100 rounded-full transition-colors relative">
              <ShoppingBag size={20} />
              <span className="absolute -top-1 -right-1 bg-black text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                0
              </span>
            </button>
          </div>
        </div>

        {/* Second Line: Sub Categories */}
        <div className="hidden md:block border-t border-gray-100 py-3">
          <nav className="flex items-center space-x-8">
            <div
              onMouseEnter={() => handleMenuHover('just-in')}
            >
              <Link
                href="/just-in"
                className="text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors"
              >
                Just In
              </Link>
            </div>
            <div
              onMouseEnter={() => handleMenuHover('designers')}
            >
              <Link
                href="/designers"
                className="text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors"
              >
                Designers
              </Link>
            </div>
            <div
              onMouseEnter={() => handleMenuHover('clothing')}
            >
              <Link
                href="/clothing"
                className="text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors"
              >
                Clothing
              </Link>
            </div>
            <div
              onMouseEnter={() => handleMenuHover('dresses')}
            >
              <Link
                href="/dresses"
                className="text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors"
              >
                Dresses
              </Link>
            </div>
            <div
              onMouseEnter={() => handleMenuHover('shoes')}
            >
              <Link
                href="/shoes"
                className="text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors"
              >
                Shoes
              </Link>
            </div>
            <div
              onMouseEnter={() => handleMenuHover('bags')}
            >
              <Link
                href="/bags"
                className="text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors"
              >
                Bags
              </Link>
            </div>
            <div
              onMouseEnter={() => handleMenuHover('accessories')}
            >
              <Link
                href="/accessories"
                className="text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors"
              >
                Accessories
              </Link>
            </div>
            <div
              onMouseEnter={() => handleMenuHover('jewellery')}
            >
              <Link
                href="/jewellery-watches"
                className="text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors"
              >
                Jewellery & Watches
              </Link>
            </div>
            <div
              onMouseEnter={() => handleMenuHover('home')}
            >
              <Link
                href="/home"
                className="text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors"
              >
                Home
              </Link>
            </div>
            <div
              onMouseEnter={() => handleMenuHover('edits')}
            >
              <Link
                href="/edits"
                className="text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors"
              >
                Edits
              </Link>
            </div>
            <div
              onMouseEnter={() => handleMenuHover('outlet')}
            >
              <Link
                href="/outlet"
                className="text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors"
              >
                Outlet
              </Link>
            </div>
          </nav>
        </div>

        {/* Unified Mega Menu Container - positioned relative to header container */}
        {activeMenu && (
          <div
            className="absolute top-full left-0 right-0 bg-white shadow-xl border-t border-gray-200 z-50"
            onMouseEnter={() => handleMenuHover(activeMenu)}
          >
            <MegaMenu type={activeMenu as 'women' | 'men' | 'just-in' | 'designers' | 'clothing' | 'dresses' | 'shoes' | 'bags' | 'accessories' | 'jewellery' | 'home' | 'edits' | 'outlet'} />
          </div>
        )}
      </div>

      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div className="fixed inset-0 z-50 md:hidden">
          <div className="fixed inset-0 bg-black bg-opacity-50" onClick={toggleMobileMenu} />
          <div className="fixed left-0 top-0 h-full w-80 bg-white shadow-xl">
            <div className="flex items-center justify-between p-4 border-b">
              <span className="text-xl font-bold">MATCHES</span>
              <button onClick={toggleMobileMenu}>
                <X size={24} />
              </button>
            </div>
            <nav className="p-4 overflow-y-auto">
              <div className="space-y-4">
                {/* Primary Categories */}
                <div className="space-y-2">
                  <Link
                    href="/womens"
                    className="block text-lg font-medium py-2 border-b border-gray-100"
                    onClick={toggleMobileMenu}
                  >
                    Women
                  </Link>
                  <Link
                    href="/mens"
                    className="block text-lg font-medium py-2 border-b border-gray-100"
                    onClick={toggleMobileMenu}
                  >
                    Men
                  </Link>
                </div>

                {/* Secondary Categories */}
                <div className="pt-4 space-y-2">
                  <h3 className="text-sm font-semibold text-gray-500 uppercase tracking-wide mb-2">Shop</h3>
                  <Link href="/just-in" className="block text-sm py-1 hover:text-gray-600" onClick={toggleMobileMenu}>Just In</Link>
                  <Link href="/designers" className="block text-sm py-1 hover:text-gray-600" onClick={toggleMobileMenu}>Designers</Link>
                  <Link href="/clothing" className="block text-sm py-1 hover:text-gray-600" onClick={toggleMobileMenu}>Clothing</Link>
                  <Link href="/dresses" className="block text-sm py-1 hover:text-gray-600" onClick={toggleMobileMenu}>Dresses</Link>
                  <Link href="/shoes" className="block text-sm py-1 hover:text-gray-600" onClick={toggleMobileMenu}>Shoes</Link>
                  <Link href="/bags" className="block text-sm py-1 hover:text-gray-600" onClick={toggleMobileMenu}>Bags</Link>
                  <Link href="/accessories" className="block text-sm py-1 hover:text-gray-600" onClick={toggleMobileMenu}>Accessories</Link>
                  <Link href="/jewellery-watches" className="block text-sm py-1 hover:text-gray-600" onClick={toggleMobileMenu}>Jewellery & Watches</Link>
                  <Link href="/home" className="block text-sm py-1 hover:text-gray-600" onClick={toggleMobileMenu}>Home</Link>
                  <Link href="/edits" className="block text-sm py-1 hover:text-gray-600" onClick={toggleMobileMenu}>Edits</Link>
                  <Link href="/outlet" className="block text-sm py-1 hover:text-gray-600" onClick={toggleMobileMenu}>Outlet</Link>
                </div>

                {/* Account & Support */}
                <div className="pt-4 space-y-2 border-t border-gray-200">
                  <h3 className="text-sm font-semibold text-gray-500 uppercase tracking-wide mb-2">Account</h3>
                  <Link href="/account" className="block text-sm py-1 hover:text-gray-600" onClick={toggleMobileMenu}>My Account</Link>
                  <Link href="/settings" className="block text-sm py-1 hover:text-gray-600" onClick={toggleMobileMenu}>Settings</Link>
                  <Link href="/help" className="block text-sm py-1 hover:text-gray-600" onClick={toggleMobileMenu}>Help Centre</Link>
                  <Link href="/delivery" className="block text-sm py-1 hover:text-gray-600" onClick={toggleMobileMenu}>Delivery</Link>
                </div>
              </div>
            </nav>
          </div>
        </div>
      )}

      {/* Search Modal */}
      <SearchModal isOpen={isSearchOpen} onClose={() => setIsSearchOpen(false)} />
    </header>
  );
};

export default Header;
