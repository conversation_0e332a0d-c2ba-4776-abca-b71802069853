'use client';

import { useState } from 'react';
import { Monitor, Smartphone, Tablet, Eye, X } from 'lucide-react';

const QCTestingPanel = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [currentBreakpoint, setCurrentBreakpoint] = useState('desktop');

  const breakpoints = [
    { id: 'mobile', name: 'Mobile', width: '375px', icon: Smartphone },
    { id: 'tablet', name: 'Tablet', width: '768px', icon: Tablet },
    { id: 'desktop', name: 'Desktop', width: '1920px', icon: Monitor },
  ];

  const testPages = [
    { name: 'Homepage', path: '/' },
    { name: 'Women\'s Landing', path: '/womens' },
    { name: 'Women\'s Clothing', path: '/womens/shop/clothing' },
    { name: 'Men\'s Landing', path: '/mens' },
    { name: 'Men\'s Clothing', path: '/mens/shop/clothing' },
  ];

  const qcChecklist = [
    'Header navigation is properly aligned',
    'Logo is clearly visible and properly sized',
    'Mega menus display correctly on hover',
    'Product grids maintain proper spacing',
    'Typography hierarchy is consistent',
    'Interactive elements have proper hover states',
    'Footer content is well-organized',
    'Mobile navigation works smoothly',
    'Search functionality is accessible',
    'Product cards display correctly',
  ];

  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-4 right-4 bg-black text-white p-3 rounded-full shadow-lg hover:bg-gray-800 transition-colors z-50"
        title="Open QC Testing Panel"
      >
        <Eye size={20} />
      </button>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-bold">QC Testing Panel - Luxury Fashion Standards</h2>
          <button
            onClick={() => setIsOpen(false)}
            className="p-2 hover:bg-gray-100 rounded-full"
          >
            <X size={20} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Breakpoint Testing */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Responsive Breakpoint Testing</h3>
            <div className="grid grid-cols-3 gap-4 mb-4">
              {breakpoints.map((bp) => {
                const IconComponent = bp.icon;
                return (
                  <button
                    key={bp.id}
                    onClick={() => setCurrentBreakpoint(bp.id)}
                    className={`p-4 border rounded-lg flex flex-col items-center space-y-2 transition-colors ${
                      currentBreakpoint === bp.id
                        ? 'border-black bg-gray-50'
                        : 'border-gray-200 hover:border-gray-400'
                    }`}
                  >
                    <IconComponent size={24} />
                    <span className="font-medium">{bp.name}</span>
                    <span className="text-sm text-gray-500">{bp.width}</span>
                  </button>
                );
              })}
            </div>
            <p className="text-sm text-gray-600">
              Current testing viewport: <strong>{breakpoints.find(bp => bp.id === currentBreakpoint)?.width}</strong>
            </p>
          </div>

          {/* Page Testing */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Page Testing</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {testPages.map((page, index) => (
                <a
                  key={index}
                  href={page.path}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-3 border border-gray-200 rounded hover:border-black transition-colors text-sm"
                >
                  {page.name} →
                </a>
              ))}
            </div>
          </div>

          {/* QC Checklist */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Luxury Fashion QC Checklist</h3>
            <div className="space-y-2">
              {qcChecklist.map((item, index) => (
                <label key={index} className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    className="w-4 h-4 text-black border-gray-300 rounded focus:ring-black"
                  />
                  <span className="text-sm">{item}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Design Standards */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Luxury Fashion Design Standards</h3>
            <div className="bg-gray-50 p-4 rounded-lg space-y-3">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <h4 className="font-medium mb-2">Typography</h4>
                  <ul className="space-y-1 text-gray-600">
                    <li>• Clean, geometric sans-serif fonts</li>
                    <li>• Consistent hierarchy and spacing</li>
                    <li>• Uppercase tracking for headings</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Layout</h4>
                  <ul className="space-y-1 text-gray-600">
                    <li>• No rounded corners (clean geometric design)</li>
                    <li>• Sophisticated spacing hierarchy</li>
                    <li>• Pixel-perfect alignment</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Interactions</h4>
                  <ul className="space-y-1 text-gray-600">
                    <li>• Smooth hover transitions</li>
                    <li>• Luxury micro-interactions</li>
                    <li>• Responsive touch targets</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Content</h4>
                  <ul className="space-y-1 text-gray-600">
                    <li>• High-quality product imagery</li>
                    <li>• Rich mega menu layouts</li>
                    <li>• Editorial content integration</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Testing Instructions */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Testing Instructions</h3>
            <div className="bg-blue-50 p-4 rounded-lg">
              <ol className="list-decimal list-inside space-y-2 text-sm">
                <li>Test each page at all three breakpoints (375px, 768px, 1920px)</li>
                <li>Verify navigation functionality and mega menu interactions</li>
                <li>Check product grid layouts and card hover states</li>
                <li>Ensure search modal opens and functions correctly</li>
                <li>Validate footer content organization and links</li>
                <li>Test mobile navigation and touch interactions</li>
                <li>Verify pixel-perfect alignment and spacing</li>
                <li>Check for luxury fashion aesthetic consistency</li>
              </ol>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QCTestingPanel;
