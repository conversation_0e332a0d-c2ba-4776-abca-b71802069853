[{"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\matches-headless-ui\\matches-fashion-clone\\src\\app\\delivery\\page.tsx": "1", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\matches-headless-ui\\matches-fashion-clone\\src\\app\\help\\page.tsx": "2", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\matches-headless-ui\\matches-fashion-clone\\src\\app\\layout.tsx": "3", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\matches-headless-ui\\matches-fashion-clone\\src\\app\\mens\\page.tsx": "4", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\matches-headless-ui\\matches-fashion-clone\\src\\app\\mens\\shop\\clothing\\page.tsx": "5", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\matches-headless-ui\\matches-fashion-clone\\src\\app\\not-found.tsx": "6", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\matches-headless-ui\\matches-fashion-clone\\src\\app\\page.tsx": "7", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\matches-headless-ui\\matches-fashion-clone\\src\\app\\returns\\page.tsx": "8", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\matches-headless-ui\\matches-fashion-clone\\src\\app\\womens\\page.tsx": "9", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\matches-headless-ui\\matches-fashion-clone\\src\\app\\womens\\shop\\clothing\\page.tsx": "10", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\matches-headless-ui\\matches-fashion-clone\\src\\components\\Footer.tsx": "11", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\matches-headless-ui\\matches-fashion-clone\\src\\components\\Header.tsx": "12", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\matches-headless-ui\\matches-fashion-clone\\src\\components\\MegaMenu.tsx": "13", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\matches-headless-ui\\matches-fashion-clone\\src\\components\\ProductCard.tsx": "14", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\matches-headless-ui\\matches-fashion-clone\\src\\components\\ProductCarousel.tsx": "15", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\matches-headless-ui\\matches-fashion-clone\\src\\components\\QCTestingPanel.tsx": "16", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\matches-headless-ui\\matches-fashion-clone\\src\\components\\SearchModal.tsx": "17"}, {"size": 4822, "mtime": 1749002775524, "results": "18", "hashOfConfig": "19"}, {"size": 4350, "mtime": 1749002747667, "results": "20", "hashOfConfig": "19"}, {"size": 964, "mtime": 1749002711096, "results": "21", "hashOfConfig": "19"}, {"size": 9141, "mtime": 1749002121894, "results": "22", "hashOfConfig": "19"}, {"size": 9045, "mtime": 1749007978364, "results": "23", "hashOfConfig": "19"}, {"size": 1035, "mtime": 1749008013779, "results": "24", "hashOfConfig": "19"}, {"size": 7374, "mtime": 1749003920176, "results": "25", "hashOfConfig": "19"}, {"size": 6180, "mtime": 1749008075380, "results": "26", "hashOfConfig": "19"}, {"size": 9944, "mtime": 1749008120118, "results": "27", "hashOfConfig": "19"}, {"size": 8545, "mtime": 1749008159274, "results": "28", "hashOfConfig": "19"}, {"size": 6774, "mtime": 1749001786436, "results": "29", "hashOfConfig": "19"}, {"size": 13712, "mtime": 1749008234968, "results": "30", "hashOfConfig": "19"}, {"size": 12928, "mtime": 1749008250992, "results": "31", "hashOfConfig": "19"}, {"size": 3541, "mtime": 1749008333607, "results": "32", "hashOfConfig": "19"}, {"size": 6769, "mtime": 1749008365623, "results": "33", "hashOfConfig": "19"}, {"size": 7609, "mtime": 1749002688094, "results": "34", "hashOfConfig": "19"}, {"size": 6980, "mtime": 1749008413735, "results": "35", "hashOfConfig": "19"}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "9atpkq", {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\matches-headless-ui\\matches-fashion-clone\\src\\app\\delivery\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\matches-headless-ui\\matches-fashion-clone\\src\\app\\help\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\matches-headless-ui\\matches-fashion-clone\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\matches-headless-ui\\matches-fashion-clone\\src\\app\\mens\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\matches-headless-ui\\matches-fashion-clone\\src\\app\\mens\\shop\\clothing\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\matches-headless-ui\\matches-fashion-clone\\src\\app\\not-found.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\matches-headless-ui\\matches-fashion-clone\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\matches-headless-ui\\matches-fashion-clone\\src\\app\\returns\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\matches-headless-ui\\matches-fashion-clone\\src\\app\\womens\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\matches-headless-ui\\matches-fashion-clone\\src\\app\\womens\\shop\\clothing\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\matches-headless-ui\\matches-fashion-clone\\src\\components\\Footer.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\matches-headless-ui\\matches-fashion-clone\\src\\components\\Header.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\matches-headless-ui\\matches-fashion-clone\\src\\components\\MegaMenu.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\matches-headless-ui\\matches-fashion-clone\\src\\components\\ProductCard.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\matches-headless-ui\\matches-fashion-clone\\src\\components\\ProductCarousel.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\matches-headless-ui\\matches-fashion-clone\\src\\components\\QCTestingPanel.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\matches-headless-ui\\matches-fashion-clone\\src\\components\\SearchModal.tsx", [], []]