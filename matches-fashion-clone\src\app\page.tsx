﻿import Link from "next/link";
import ProductCarousel from "@/components/ProductCarousel";

export default function Home() {
  // Mock product data for carousel
  const justInProducts = [
    { id: '1', name: 'Oversized striped cashmere rugby shirt', designer: '<PERSON><PERSON>', image: '', href: '/womens/product/1' },
    { id: '2', name: 'T-Lock small grained-leather cross-body bag', designer: 'Toteme', image: '', href: '/womens/product/2' },
    { id: '3', name: 'Duomo pleated linen straight-leg trousers', designer: 'Faithfull The Brand', image: '', href: '/womens/product/3' },
    { id: '4', name: 'Ava leather Mary Jane flats', designer: 'The Row', image: '', href: '/womens/product/4' },
    { id: '5', name: 'Anagram-logo small leather-trim raffia basket bag', designer: 'LOEWE', image: '', href: '/womens/product/5' },
    { id: '6', name: 'Scoop-neck organic cotton-blend tank top', designer: 'Toteme', image: '', href: '/womens/product/6' },
    { id: '7', name: 'The Asymmetric leather ballet flats', designer: 'Toteme', image: '', href: '/womens/product/7' },
    { id: '8', name: 'Patch-pocket cotton blend-twill trousers', designer: 'Frame', image: '', href: '/womens/product/8' },
    { id: '9', name: 'Anagram-logo leather-trim raffia basket bag', designer: 'LOEWE', image: '', href: '/womens/product/9' },
    { id: '10', name: 'Hinged 18kt gold-vermeil hoop earrings', designer: 'Sophie Buhai', image: '', href: '/womens/product/10' },
    { id: '11', name: 'Puff-sleeve pleated midi dress', designer: 'Zimmermann', image: '', href: '/womens/product/11' },
    { id: '12', name: 'Rockstud 60 leather block-heel sandals', designer: 'Valentino Garavani', image: '', href: '/womens/product/12' },
  ];

  return (
    <div className="bg-white">
      {/* Hero Section - Large Image Block */}
      <section className="relative">
        <div className="aspect-[16/9] lg:aspect-[21/9] bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
          <div className="w-full h-full flex items-center justify-center">
            <span className="text-gray-500 text-lg">Hero Image</span>
          </div>
        </div>
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center text-black">
            <p className="text-sm uppercase tracking-wide mb-2">24/7 STYLE</p>
            <h1 className="text-4xl md:text-6xl font-bold mb-4">SHOP</h1>
          </div>
        </div>
      </section>

      {/* Product Carousel - Just In */}
      <ProductCarousel
        title="NEW STYLES"
        subtitle="JUST LANDED"
        count={545}
        ctaText="Shop Now"
        ctaHref="/womens/just-in/just-in-this-month"
        products={justInProducts}
        type="just-in"
      />

      {/* Main Content Grid - Exact Layout Match */}
      <section className="max-w-7xl mx-auto px-4 py-8">
        {/* First Row - Two Large Blocks (4:5 aspect ratio) */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
          {/* Left Block - Editorial Content */}
          <Link href="/womens/stories" className="group relative overflow-hidden">
            <div className="aspect-[4/5] bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
              <span className="text-gray-500">Editorial Image</span>
            </div>
            <div className="absolute bottom-0 left-0 right-0 bg-white bg-opacity-90 p-4">
              <h3 className="text-sm font-medium uppercase tracking-wide">STYLE INSPIRATION</h3>
              <p className="text-xs text-gray-600 mt-1">Discover the latest trends</p>
            </div>
          </Link>

          {/* Right Block - Featured Product */}
          <Link href="/womens/shop/shoes" className="group relative overflow-hidden">
            <div className="aspect-[4/5] bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
              <span className="text-gray-500">Product Image</span>
            </div>
            <div className="absolute bottom-0 left-0 right-0 bg-white bg-opacity-90 p-4">
              <h3 className="text-sm font-medium uppercase tracking-wide">THE EDITORS</h3>
              <p className="text-xs text-gray-600 mt-1">Curated selections</p>
            </div>
          </Link>
        </div>

        {/* Second Row - Three Medium Blocks (3:4 aspect ratio) */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <Link href="/womens/shop/clothing/dresses" className="group relative overflow-hidden">
            <div className="aspect-[3/4] bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
              <span className="text-gray-500">Dress Image</span>
            </div>
            <div className="absolute bottom-0 left-0 right-0 bg-white bg-opacity-90 p-3">
              <h3 className="text-xs font-medium uppercase tracking-wide">DRESSES</h3>
            </div>
          </Link>

          <Link href="/womens/shop/bags" className="group relative overflow-hidden">
            <div className="aspect-[3/4] bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
              <span className="text-gray-500">Bag Image</span>
            </div>
            <div className="absolute bottom-0 left-0 right-0 bg-white bg-opacity-90 p-3">
              <h3 className="text-xs font-medium uppercase tracking-wide">BAGS</h3>
            </div>
          </Link>

          <Link href="/womens/shop/clothing/knitwear" className="group relative overflow-hidden">
            <div className="aspect-[3/4] bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
              <span className="text-gray-500">Knitwear Image</span>
            </div>
            <div className="absolute bottom-0 left-0 right-0 bg-white bg-opacity-90 p-3">
              <h3 className="text-xs font-medium uppercase tracking-wide">KNITWEAR</h3>
            </div>
          </Link>
        </div>

        {/* Third Row - Two Large Blocks (4:5 aspect ratio) */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <Link href="/womens/designers/the-row" className="group relative overflow-hidden">
            <div className="aspect-[4/5] bg-gradient-to-br from-gray-200 to-gray-400 flex items-center justify-center">
              <span className="text-gray-500">Designer Feature</span>
            </div>
            <div className="absolute bottom-0 left-0 right-0 bg-white bg-opacity-90 p-4">
              <h3 className="text-sm font-medium uppercase tracking-wide">DESIGNER SPOTLIGHT</h3>
              <p className="text-xs text-gray-600 mt-1">Exclusive collections</p>
            </div>
          </Link>

          <Link href="/womens/just-in" className="group relative overflow-hidden">
            <div className="aspect-[4/5] bg-gradient-to-br from-gray-300 to-gray-500 flex items-center justify-center">
              <span className="text-gray-500">New Arrivals</span>
            </div>
            <div className="absolute bottom-0 left-0 right-0 bg-white bg-opacity-90 p-4">
              <h3 className="text-sm font-medium uppercase tracking-wide">NEW ARRIVALS</h3>
              <p className="text-xs text-gray-600 mt-1">Just landed</p>
            </div>
          </Link>
        </div>
      </section>
    </div>
  );
}
