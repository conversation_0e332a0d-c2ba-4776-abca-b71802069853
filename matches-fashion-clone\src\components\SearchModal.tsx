'use client';

import { useState, useEffect } from 'react';
import { X, Search, TrendingUp } from 'lucide-react';
import Link from 'next/link';

interface SearchModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface Product {
  id: number;
  name: string;
  brand: string;
  price: number;
  category: string;
}

// Mock products data - moved outside component to avoid dependency issues
const mockProducts: Product[] = [
  { id: 1, name: 'Ophidia GG Supreme bag', brand: 'Gucci', price: 1200, category: 'Bags' },
  { id: 2, name: 'Opyum pumps', brand: 'Saint Laurent', price: 895, category: 'Shoes' },
  { id: 3, name: 'Cashmere coat', brand: 'The Row', price: 2890, category: 'Coats' },
  { id: 4, name: 'Intrecciato wallet', brand: 'Bottega Veneta', price: 450, category: 'Accessories' },
];

const SearchModal = ({ isOpen, onClose }: SearchModalProps) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<Product[]>([]);

  // Mock search data
  const trendingSearches = [
    'Gucci bags',
    'Saint Laurent shoes',
    'The Row coats',
    'Bottega Veneta',
    'Khaite dresses',
    'Designer jeans',
    'Luxury sneakers',
    'Evening dresses'
  ];

  useEffect(() => {
    if (searchQuery.length > 2) {
      // Simulate search results
      const filtered = mockProducts.filter(product =>
        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.brand.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.category.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setSearchResults(filtered);
    } else {
      setSearchResults([]);
    }
  }, [searchQuery]);

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 bg-white">
      {/* Header */}
      <div className="border-b border-gray-200 p-4">
        <div className="max-w-4xl mx-auto flex items-center space-x-4">
          <div className="flex-1 relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
            <input
              type="text"
              placeholder="Search for designers, products, or categories..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-12 pr-4 py-3 text-lg border-none outline-none"
              autoFocus
            />
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <X size={24} />
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto p-4">
        {searchQuery.length === 0 ? (
          /* Trending Searches */
          <div>
            <h3 className="text-lg font-semibold mb-4 flex items-center">
              <TrendingUp size={20} className="mr-2" />
              Trending Searches
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {trendingSearches.map((search, index) => (
                <button
                  key={index}
                  onClick={() => setSearchQuery(search)}
                  className="text-left p-3 border border-gray-200 hover:border-black transition-colors text-sm"
                >
                  {search}
                </button>
              ))}
            </div>

            {/* Popular Categories */}
            <div className="mt-8">
              <h3 className="text-lg font-semibold mb-4">Popular Categories</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Link href="/womens/shop/clothing/dresses" className="group">
                  <div className="aspect-[4/3] bg-gradient-to-br from-gray-100 to-gray-200 mb-3 flex items-center justify-center">
                    <span className="text-gray-500 text-sm">Dresses</span>
                  </div>
                  <h4 className="font-medium group-hover:text-gray-600">Women&apos;s Dresses</h4>
                </Link>
                <Link href="/womens/shop/bags" className="group">
                  <div className="aspect-[4/3] bg-gradient-to-br from-gray-100 to-gray-200 mb-3 flex items-center justify-center">
                    <span className="text-gray-500 text-sm">Bags</span>
                  </div>
                  <h4 className="font-medium group-hover:text-gray-600">Designer Bags</h4>
                </Link>
                <Link href="/mens/shop/clothing/suits" className="group">
                  <div className="aspect-[4/3] bg-gradient-to-br from-gray-100 to-gray-200 mb-3 flex items-center justify-center">
                    <span className="text-gray-500 text-sm">Suits</span>
                  </div>
                  <h4 className="font-medium group-hover:text-gray-600">Men&apos;s Suits</h4>
                </Link>
              </div>
            </div>
          </div>
        ) : (
          /* Search Results */
          <div>
            <h3 className="text-lg font-semibold mb-4">
              Search Results for &quot;{searchQuery}&quot; ({searchResults.length})
            </h3>
            {searchResults.length > 0 ? (
              <div className="space-y-4">
                {searchResults.map((product) => (
                  <Link
                    key={product.id}
                    href={`/product/${product.id}`}
                    className="flex items-center space-x-4 p-4 hover:bg-gray-50 transition-colors"
                    onClick={onClose}
                  >
                    <div className="w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                      <span className="text-xs text-gray-500">IMG</span>
                    </div>
                    <div className="flex-1">
                      <p className="text-xs text-gray-500 uppercase tracking-wide">{product.brand}</p>
                      <h4 className="font-medium">{product.name}</h4>
                      <p className="text-sm text-gray-600">£{product.price}</p>
                    </div>
                    <div className="text-xs text-gray-500">{product.category}</div>
                  </Link>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <p className="text-gray-500 mb-4">No results found for &quot;{searchQuery}&quot;</p>
                <p className="text-sm text-gray-400">Try searching for designers, product names, or categories</p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default SearchModal;
