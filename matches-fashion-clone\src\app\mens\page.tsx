import Link from "next/link";

export default function MensPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative h-screen flex items-center justify-center">
        <div className="absolute inset-0 bg-gradient-to-br from-gray-200 to-gray-400"></div>
        <div className="relative z-10 text-center text-white">
          <p className="text-sm uppercase tracking-wide mb-4">introducing the new collections</p>
          <h1 className="text-6xl md:text-8xl font-bold mb-6">Modern Luxury</h1>
          <button className="luxury-button">SHOP</button>
        </div>
      </section>

      {/* Navigation Bar */}
      <nav className="bg-white border-b border-gray-200 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4">
          <div className="flex justify-center space-x-8 py-4">
            <Link href="/mens/designers" className="text-sm font-medium uppercase tracking-wide hover:text-gray-600">
              Designers
            </Link>
            <Link href="/mens/shop/clothing" className="text-sm font-medium uppercase tracking-wide hover:text-gray-600">
              Clothing
            </Link>
            <Link href="/mens/shop/shoes" className="text-sm font-medium uppercase tracking-wide hover:text-gray-600">
              Shoes
            </Link>
            <Link href="/mens/shop/bags" className="text-sm font-medium uppercase tracking-wide hover:text-gray-600">
              Bags
            </Link>
            <Link href="/mens/shop/accessories" className="text-sm font-medium uppercase tracking-wide hover:text-gray-600">
              Accessories
            </Link>
            <Link href="/mens/stories" className="text-sm font-medium uppercase tracking-wide hover:text-gray-600">
              Stories
            </Link>
          </div>
        </div>
      </nav>

      {/* Featured Content Grid */}
      <section className="max-w-7xl mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {/* Featured Item 1 */}
          <Link href="/mens/shop/clothing/suits" className="group">
            <div className="aspect-[3/4] bg-gradient-to-br from-gray-200 to-gray-300 mb-4 overflow-hidden">
              <div className="w-full h-full flex items-center justify-center">
                <span className="text-gray-500">Suits Image</span>
              </div>
            </div>
            <div className="space-y-2">
              <p className="text-xs uppercase tracking-wide text-gray-500">tailored perfection</p>
              <h3 className="text-lg font-medium">Modern Suiting</h3>
              <button className="text-sm font-medium uppercase tracking-wide">SHOP</button>
            </div>
          </Link>

          {/* Featured Item 2 */}
          <Link href="/mens/shop/shoes/sneakers" className="group">
            <div className="aspect-[3/4] bg-gradient-to-br from-gray-200 to-gray-300 mb-4 overflow-hidden">
              <div className="w-full h-full flex items-center justify-center">
                <span className="text-gray-500">Sneakers Image</span>
              </div>
            </div>
            <div className="space-y-2">
              <p className="text-xs uppercase tracking-wide text-gray-500">street luxury</p>
              <h3 className="text-lg font-medium">Premium Sneakers</h3>
              <button className="text-sm font-medium uppercase tracking-wide">SHOP</button>
            </div>
          </Link>

          {/* Featured Item 3 */}
          <Link href="/mens/designers/tom-ford" className="group">
            <div className="aspect-[3/4] bg-gradient-to-br from-gray-200 to-gray-300 mb-4 overflow-hidden">
              <div className="w-full h-full flex items-center justify-center">
                <span className="text-gray-500">Tom Ford Image</span>
              </div>
            </div>
            <div className="space-y-2">
              <p className="text-xs uppercase tracking-wide text-gray-500">sophisticated elegance</p>
              <h3 className="text-lg font-medium">Tom Ford</h3>
              <button className="text-sm font-medium uppercase tracking-wide">SHOP</button>
            </div>
          </Link>

          {/* Featured Item 4 */}
          <Link href="/mens/shop/bags" className="group">
            <div className="aspect-[3/4] bg-gradient-to-br from-gray-200 to-gray-300 mb-4 overflow-hidden">
              <div className="w-full h-full flex items-center justify-center">
                <span className="text-gray-500">Bags Image</span>
              </div>
            </div>
            <div className="space-y-2">
              <p className="text-xs uppercase tracking-wide text-gray-500">functional luxury</p>
              <h3 className="text-lg font-medium">Essential Bags</h3>
              <button className="text-sm font-medium uppercase tracking-wide">SHOP</button>
            </div>
          </Link>

          {/* Featured Item 5 */}
          <Link href="/mens/shop/clothing/knitwear" className="group">
            <div className="aspect-[3/4] bg-gradient-to-br from-gray-200 to-gray-300 mb-4 overflow-hidden">
              <div className="w-full h-full flex items-center justify-center">
                <span className="text-gray-500">Knitwear Image</span>
              </div>
            </div>
            <div className="space-y-2">
              <p className="text-xs uppercase tracking-wide text-gray-500">comfort meets style</p>
              <h3 className="text-lg font-medium">Luxury Knitwear</h3>
              <button className="text-sm font-medium uppercase tracking-wide">SHOP</button>
            </div>
          </Link>

          {/* Featured Item 6 */}
          <Link href="/mens/shop/accessories/watches" className="group">
            <div className="aspect-[3/4] bg-gradient-to-br from-gray-200 to-gray-300 mb-4 overflow-hidden">
              <div className="w-full h-full flex items-center justify-center">
                <span className="text-gray-500">Watches Image</span>
              </div>
            </div>
            <div className="space-y-2">
              <p className="text-xs uppercase tracking-wide text-gray-500">timeless precision</p>
              <h3 className="text-lg font-medium">Luxury Timepieces</h3>
              <button className="text-sm font-medium uppercase tracking-wide">SHOP</button>
            </div>
          </Link>
        </div>

        {/* Large Featured Items */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-12">
          <Link href="/mens/lists/workwear" className="group">
            <div className="aspect-[4/3] bg-gradient-to-br from-gray-200 to-gray-300 mb-4 overflow-hidden">
              <div className="w-full h-full flex items-center justify-center">
                <span className="text-gray-500">Workwear Image</span>
              </div>
            </div>
            <div className="space-y-2">
              <p className="text-xs uppercase tracking-wide text-gray-500">professional style</p>
              <h3 className="text-xl font-medium">Executive Essentials</h3>
              <button className="text-sm font-medium uppercase tracking-wide">SHOP</button>
            </div>
          </Link>

          <Link href="/mens/lists/weekend" className="group">
            <div className="aspect-[4/3] bg-gradient-to-br from-gray-200 to-gray-300 mb-4 overflow-hidden">
              <div className="w-full h-full flex items-center justify-center">
                <span className="text-gray-500">Weekend Image</span>
              </div>
            </div>
            <div className="space-y-2">
              <p className="text-xs uppercase tracking-wide text-gray-500">relaxed luxury</p>
              <h3 className="text-xl font-medium">Weekend Edit</h3>
              <button className="text-sm font-medium uppercase tracking-wide">SHOP</button>
            </div>
          </Link>
        </div>

        {/* Featured Designers */}
        <div className="mt-16">
          <h2 className="text-2xl font-bold text-center mb-8">Featured Designers</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8">
            {[
              'Tom Ford',
              'Brunello Cucinelli',
              'Stone Island',
              'Thom Browne',
              'Bottega Veneta',
              'Gucci'
            ].map((designer, index) => (
              <Link
                key={index}
                href={`/mens/designers/${designer.toLowerCase().replace(/\s+/g, '-')}`}
                className="text-center group"
              >
                <div className="aspect-square bg-gradient-to-br from-gray-100 to-gray-200 mb-3 flex items-center justify-center group-hover:from-gray-200 group-hover:to-gray-300 transition-all duration-300">
                  <span className="text-xs text-gray-600">Logo</span>
                </div>
                <h3 className="text-sm font-medium">{designer}</h3>
              </Link>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
}
