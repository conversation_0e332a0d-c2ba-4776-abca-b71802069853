import Link from 'next/link';

export default function ReturnsPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Breadcrumb */}
      <div className="max-w-7xl mx-auto px-4 py-4">
        <nav className="text-sm text-gray-500">
          <Link href="/" className="hover:text-black">Home</Link>
          <span className="mx-2">/</span>
          <span className="text-black">Returns</span>
        </nav>
      </div>

      <div className="max-w-4xl mx-auto px-4 py-12">
        <h1 className="text-4xl font-bold mb-8">Returns & Exchanges</h1>

        <div className="space-y-8">
          {/* Return Policy */}
          <section>
            <h2 className="text-2xl font-semibold mb-4">Return Policy</h2>
            <div className="bg-gray-50 p-6 space-y-4">
              <p className="text-gray-600">
                We want you to be completely satisfied with your purchase. If you&apos;re not happy with your order,
                you can return it within 28 days of delivery for a full refund.
              </p>
              <ul className="list-disc list-inside space-y-2 text-gray-600">
                <li>Items must be in original condition with all tags attached</li>
                <li>Items must be unworn and in original packaging</li>
                <li>Personalized or made-to-order items cannot be returned</li>
                <li>Underwear, swimwear, and pierced jewelry cannot be returned for hygiene reasons</li>
              </ul>
            </div>
          </section>

          {/* How to Return */}
          <section>
            <h2 className="text-2xl font-semibold mb-4">How to Return</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="font-medium mb-3">UK Returns</h3>
                <ol className="list-decimal list-inside space-y-2 text-sm text-gray-600">
                  <li>Log into your account and select the items to return</li>
                  <li>Print your prepaid return label</li>
                  <li>Package items securely in original packaging</li>
                  <li>Attach the return label and drop off at any Royal Mail post office</li>
                </ol>
                <p className="text-sm font-medium mt-3">FREE returns for UK customers</p>
              </div>
              <div>
                <h3 className="font-medium mb-3">International Returns</h3>
                <ol className="list-decimal list-inside space-y-2 text-sm text-gray-600">
                  <li>Contact our customer service team</li>
                  <li>We&apos;ll provide return instructions and label</li>
                  <li>Package items securely</li>
                  <li>Send via your local postal service</li>
                </ol>
                <p className="text-sm font-medium mt-3">Return shipping costs apply</p>
              </div>
            </div>
          </section>

          {/* Exchanges */}
          <section>
            <h2 className="text-2xl font-semibold mb-4">Exchanges</h2>
            <div className="bg-gray-50 p-6">
              <p className="text-gray-600 mb-4">
                We don&apos;t offer direct exchanges. To exchange an item, please return your original purchase
                and place a new order for the item you want.
              </p>
              <p className="text-sm text-gray-600">
                This ensures you receive your new item as quickly as possible and aren&apos;t charged twice.
              </p>
            </div>
          </section>

          {/* Refunds */}
          <section>
            <h2 className="text-2xl font-semibold mb-4">Refunds</h2>
            <div className="space-y-4">
              <p className="text-gray-600">
                Once we receive your return, we&apos;ll inspect the items and process your refund within 5-10 working days.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-medium mb-2">Refund Methods</h3>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Original payment method</li>
                    <li>• Store credit (if preferred)</li>
                    <li>• Gift card (for gift purchases)</li>
                  </ul>
                </div>
                <div>
                  <h3 className="font-medium mb-2">Processing Times</h3>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Credit/Debit cards: 3-5 working days</li>
                    <li>• PayPal: 1-2 working days</li>
                    <li>• Bank transfer: 5-7 working days</li>
                  </ul>
                </div>
              </div>
            </div>
          </section>

          {/* Damaged Items */}
          <section>
            <h2 className="text-2xl font-semibold mb-4">Damaged or Faulty Items</h2>
            <div className="bg-red-50 border border-red-200 p-6">
              <p className="text-gray-700 mb-4">
                If you receive a damaged or faulty item, please contact us immediately. We&apos;ll arrange for a
                replacement or full refund, including return shipping costs.
              </p>
              <p className="text-sm text-gray-600">
                Please take photos of any damage and include them when contacting our customer service team.
              </p>
            </div>
          </section>

          {/* Contact */}
          <section className="bg-gray-50 p-6 text-center">
            <h3 className="font-semibold mb-2">Need help with a return?</h3>
            <p className="text-gray-600 mb-4">Our customer service team is here to help</p>
            <div className="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
              <Link href="/help" className="luxury-button-outline">
                Contact Us
              </Link>
              <button className="luxury-button">
                Start Return
              </button>
            </div>
          </section>
        </div>
      </div>
    </div>
  );
}
