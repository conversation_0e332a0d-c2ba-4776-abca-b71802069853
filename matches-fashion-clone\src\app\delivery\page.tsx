import Link from 'next/link';

export default function DeliveryPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Breadcrumb */}
      <div className="max-w-7xl mx-auto px-4 py-4">
        <nav className="text-sm text-gray-500">
          <Link href="/" className="hover:text-black">Home</Link>
          <span className="mx-2">/</span>
          <span className="text-black">Delivery</span>
        </nav>
      </div>

      <div className="max-w-4xl mx-auto px-4 py-12">
        <h1 className="text-4xl font-bold mb-8">Delivery Information</h1>

        <div className="space-y-8">
          {/* UK Delivery */}
          <section>
            <h2 className="text-2xl font-semibold mb-4">UK Delivery</h2>
            <div className="bg-gray-50 p-6 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-medium mb-2">Standard Delivery</h3>
                  <p className="text-sm text-gray-600 mb-2">3-5 working days</p>
                  <p className="text-sm">FREE on orders over £200</p>
                  <p className="text-sm">£5 on orders under £200</p>
                </div>
                <div>
                  <h3 className="font-medium mb-2">Express Delivery</h3>
                  <p className="text-sm text-gray-600 mb-2">Next working day</p>
                  <p className="text-sm">£15 (order by 2pm)</p>
                </div>
              </div>
            </div>
          </section>

          {/* International Delivery */}
          <section>
            <h2 className="text-2xl font-semibold mb-4">International Delivery</h2>
            <div className="bg-gray-50 p-6">
              <p className="text-gray-600 mb-4">
                We deliver to over 190 countries worldwide. Delivery times and costs vary by destination.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <h4 className="font-medium">Europe</h4>
                  <p className="text-gray-600">3-7 working days</p>
                  <p>From £15</p>
                </div>
                <div>
                  <h4 className="font-medium">USA & Canada</h4>
                  <p className="text-gray-600">5-10 working days</p>
                  <p>From £25</p>
                </div>
                <div>
                  <h4 className="font-medium">Rest of World</h4>
                  <p className="text-gray-600">7-14 working days</p>
                  <p>From £35</p>
                </div>
              </div>
            </div>
          </section>

          {/* Order Processing */}
          <section>
            <h2 className="text-2xl font-semibold mb-4">Order Processing</h2>
            <div className="space-y-4">
              <p className="text-gray-600">
                Orders are processed Monday to Friday, excluding bank holidays. Orders placed after 2pm on Friday 
                will be processed the following Monday.
              </p>
              <ul className="list-disc list-inside space-y-2 text-gray-600">
                <li>You will receive an order confirmation email immediately after placing your order</li>
                <li>A dispatch confirmation with tracking information will be sent when your order ships</li>
                <li>Track your order using the link provided in your dispatch email</li>
              </ul>
            </div>
          </section>

          {/* Special Services */}
          <section>
            <h2 className="text-2xl font-semibold mb-4">Special Delivery Services</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="border border-gray-200 p-6">
                <h3 className="font-medium mb-3">Saturday Delivery</h3>
                <p className="text-sm text-gray-600 mb-2">Available for UK orders</p>
                <p className="text-sm">£25 additional charge</p>
              </div>
              <div className="border border-gray-200 p-6">
                <h3 className="font-medium mb-3">Nominated Day Delivery</h3>
                <p className="text-sm text-gray-600 mb-2">Choose your delivery date</p>
                <p className="text-sm">£20 additional charge</p>
              </div>
            </div>
          </section>

          {/* Contact */}
          <section className="bg-gray-50 p-6 text-center">
            <h3 className="font-semibold mb-2">Need help with your delivery?</h3>
            <p className="text-gray-600 mb-4">Contact our customer service team for assistance</p>
            <Link href="/help" className="luxury-button-outline">
              Contact Us
            </Link>
          </section>
        </div>
      </div>
    </div>
  );
}
