1:"$Sreact.fragment"
2:I[8503,["874","static/chunks/874-4ab02debfaf779a6.js","177","static/chunks/app/layout-9b90e7ab0b316405.js"],"default"]
3:I[7555,[],""]
4:I[1295,[],""]
5:I[6874,["874","static/chunks/874-4ab02debfaf779a6.js","728","static/chunks/app/help/page-a27064719b0a01a9.js"],""]
6:I[6987,["874","static/chunks/874-4ab02debfaf779a6.js","177","static/chunks/app/layout-9b90e7ab0b316405.js"],"default"]
7:I[2277,["874","static/chunks/874-4ab02debfaf779a6.js","177","static/chunks/app/layout-9b90e7ab0b316405.js"],"default"]
8:I[9665,[],"MetadataBoundary"]
a:I[9665,[],"OutletBoundary"]
d:I[4911,[],"AsyncMetadataOutlet"]
f:I[9665,[],"ViewportBoundary"]
11:I[6614,[],""]
:HL["/_next/static/css/1e19055866d1f255.css","style"]
0:{"P":null,"b":"GfHiHsXNIhYeWhao3WpiJ","p":"","c":["","help"],"i":false,"f":[[["",{"children":["help",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/1e19055866d1f255.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","children":["$","body",null,{"className":"min-h-screen flex flex-col antialiased","children":[["$","$L2",null,{}],["$","main",null,{"className":"flex-1","children":["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[["$","div",null,{"className":"min-h-screen flex items-center justify-center bg-white","children":["$","div",null,{"className":"text-center max-w-md mx-auto px-4","children":[["$","h1",null,{"className":"text-6xl font-bold mb-4","children":"404"}],["$","h2",null,{"className":"text-2xl font-semibold mb-4","children":"Page Not Found"}],["$","p",null,{"className":"text-gray-600 mb-8","children":"The page you are looking for might have been removed, had its name changed, or is temporarily unavailable."}],["$","div",null,{"className":"space-y-4","children":[["$","$L5",null,{"href":"/","className":"luxury-button block","children":"Return Home"}],["$","div",null,{"className":"flex justify-center space-x-4 text-sm","children":[["$","$L5",null,{"href":"/womens","className":"text-gray-600 hover:text-black","children":"Shop Women's"}],["$","$L5",null,{"href":"/mens","className":"text-gray-600 hover:text-black","children":"Shop Men's"}]]}]]}]]}]}],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}],["$","$L6",null,{}],["$","$L7",null,{}]]}]}]]}],{"children":["help",["$","$1","c",{"children":[null,["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","div",null,{"className":"min-h-screen bg-white","children":[["$","div",null,{"className":"max-w-7xl mx-auto px-4 py-4","children":["$","nav",null,{"className":"text-sm text-gray-500","children":[["$","$L5",null,{"href":"/","className":"hover:text-black","children":"Home"}],["$","span",null,{"className":"mx-2","children":"/"}],["$","span",null,{"className":"text-black","children":"Help Centre"}]]}]}],["$","div",null,{"className":"max-w-7xl mx-auto px-4 py-12","children":[["$","div",null,{"className":"text-center mb-12","children":[["$","h1",null,{"className":"text-4xl font-bold mb-4","children":"Help Centre"}],["$","p",null,{"className":"text-gray-600 max-w-2xl mx-auto","children":"Find answers to your questions about shopping, delivery, returns, and more. Our customer service team is here to help you with your luxury shopping experience."}]]}],["$","div",null,{"className":"max-w-2xl mx-auto mb-12","children":["$","div",null,{"className":"relative","children":[["$","input",null,{"type":"text","placeholder":"Search for help topics...","className":"w-full px-6 py-4 border border-gray-300 focus:outline-none focus:border-black text-lg"}],["$","button",null,{"className":"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400","children":"Search"}]]}]}],["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12","children":[["$","div","0",{"className":"space-y-4","children":[["$","h3",null,{"className":"font-semibold text-lg","children":"Orders & Delivery"}],["$","ul",null,{"className":"space-y-2","children":[["$","li","0",{"children":["$","$L5",null,{"href":"/help/track-order","className":"text-gray-600 hover:text-black transition-colors text-sm","children":"Track your order"}]}],["$","li","1",{"children":["$","$L5",null,{"href":"/delivery","className":"text-gray-600 hover:text-black transition-colors text-sm","children":"Delivery information"}]}],["$","li","2",{"children":["$","$L5",null,{"href":"/help/delivery-charges","className":"text-gray-600 hover:text-black transition-colors text-sm","children":"Delivery charges"}]}],["$","li","3",{"children":["$","$L5",null,{"href":"/help/international-delivery","className":"text-gray-600 hover:text-black transition-colors text-sm","children":"International delivery"}]}]]}]]}],["$","div","1",{"className":"space-y-4","children":[["$","h3",null,{"className":"font-semibold text-lg","children":"Returns & Exchanges"}],["$","ul",null,{"className":"space-y-2","children":[["$","li","0",{"children":["$","$L5",null,{"href":"/returns","className":"text-gray-600 hover:text-black transition-colors text-sm","children":"Return policy"}]}],["$","li","1",{"children":["$","$L5",null,{"href":"/help/how-to-return","className":"text-gray-600 hover:text-black transition-colors text-sm","children":"How to return"}]}],["$","li","2",{"children":["$","$L5",null,{"href":"/help/exchanges","className":"text-gray-600 hover:text-black transition-colors text-sm","children":"Exchanges"}]}],["$","li","3",{"children":["$","$L5",null,{"href":"/help/refunds","className":"text-gray-600 hover:text-black transition-colors text-sm","children":"Refunds"}]}]]}]]}],["$","div","2",{"className":"space-y-4","children":[["$","h3",null,{"className":"font-semibold text-lg","children":"Account & Shopping"}],["$","ul",null,{"className":"space-y-2","children":[["$","li","0",{"children":["$","$L5",null,{"href":"/help/create-account","className":"text-gray-600 hover:text-black transition-colors text-sm","children":"Create an account"}]}],["$","li","1",{"children":["$","$L5",null,{"href":"/help/manage-account","className":"text-gray-600 hover:text-black transition-colors text-sm","children":"Manage your account"}]}],["$","li","2",{"children":["$","$L5",null,{"href":"/size-guide","className":"text-gray-600 hover:text-black transition-colors text-sm","children":"Size guide"}]}],["$","li","3",{"children":["$","$L5",null,{"href":"/help/product-care","className":"text-gray-600 hover:text-black transition-colors text-sm","children":"Product care"}]}]]}]]}],["$","div","3",{"className":"space-y-4","children":[["$","h3",null,{"className":"font-semibold text-lg","children":"Payment & Pricing"}],["$","ul",null,{"className":"space-y-2","children":[["$","li","0",{"children":["$","$L5",null,{"href":"/help/payment-methods","className":"text-gray-600 hover:text-black transition-colors text-sm","children":"Payment methods"}]}],["$","li","1",{"children":["$","$L5",null,{"href":"/help/currency-pricing","className":"text-gray-600 hover:text-black transition-colors text-sm","children":"Currency & pricing"}]}],["$","li","2",{"children":["$","$L5",null,{"href":"/gift-cards","className":"text-gray-600 hover:text-black transition-colors text-sm","children":"Gift cards"}]}],["$","li","3",{"children":["$","$L5",null,{"href":"/help/promotional-codes","className":"text-gray-600 hover:text-black transition-colors text-sm","children":"Promotional codes"}]}]]}]]}]]}],["$","div",null,{"className":"bg-gray-50 p-8 text-center","children":[["$","h2",null,{"className":"text-2xl font-bold mb-4","children":"Still need help?"}],["$","p",null,{"className":"text-gray-600 mb-6","children":"Our customer service team is available to assist you with any questions."}],["$","div",null,{"className":"flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4","children":[["$","button",null,{"className":"luxury-button","children":"Live Chat"}],["$","button",null,{"className":"luxury-button-outline","children":"Email Us"}]]}],["$","div",null,{"className":"mt-6 text-sm text-gray-500","children":["$","p",null,{"children":"Customer Service Hours: Monday - Friday, 9AM - 6PM GMT"}]}]]}]]}]]}],["$","$L8",null,{"children":"$L9"}],null,["$","$La",null,{"children":["$Lb","$Lc",["$","$Ld",null,{"promise":"$@e"}]]}]]}],{},null,false]},null,false]},null,false],["$","$1","h",{"children":[null,["$","$1","8Ob0YPys4AGWOZjNvVQOP",{"children":[["$","$Lf",null,{"children":"$L10"}],null]}],null]}],false]],"m":"$undefined","G":["$11","$undefined"],"s":false,"S":true}
12:"$Sreact.suspense"
13:I[4911,[],"AsyncMetadata"]
9:["$","$12",null,{"fallback":null,"children":["$","$L13",null,{"promise":"$@14"}]}]
c:null
10:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
b:null
14:{"metadata":[["$","title","0",{"children":"Luxury Fashion | Designer Clothing, Bags & Shoes | MATCHES UK"}],["$","meta","1",{"name":"description","content":"Discover luxury fashion at MATCHES. Shop designer clothing, bags, shoes and accessories from over 450 established and innovative designer brands."}],["$","meta","2",{"name":"keywords","content":"luxury fashion, designer clothing, designer bags, designer shoes, luxury accessories, high-end fashion"}],["$","link","3",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}]],"error":null,"digest":"$undefined"}
e:{"metadata":"$14:metadata","error":null,"digest":"$undefined"}
