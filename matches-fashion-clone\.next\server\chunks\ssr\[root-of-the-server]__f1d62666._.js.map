{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/matches-headless-ui/matches-fashion-clone/src/components/MegaMenu.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\n\ninterface MegaMenuProps {\n  type: 'women' | 'men' | 'just-in' | 'designers' | 'clothing' | 'dresses' | 'shoes' | 'bags' | 'accessories' | 'jewellery' | 'home' | 'edits' | 'outlet';\n}\n\nconst MegaMenu = ({ type }: MegaMenuProps) => {\n  const womenCategories = [\n    {\n      title: 'Just In',\n      links: [\n        { name: 'Just in this month', href: '/womens/just-in/just-in-this-month' },\n        { name: 'Just in 7 days', href: '/womens/just-in/just-in-last-7-days' },\n        { name: 'Back in stock', href: '/womens/lists/back-in-stock' },\n        { name: 'Coming soon', href: '/womens/lists/coming-soon' },\n        { name: 'Exclusives', href: '/womens/lists/exclusives' },\n      ]\n    },\n    {\n      title: 'Clothing',\n      links: [\n        { name: 'Shop all', href: '/womens/shop/clothing' },\n        { name: 'Dresses', href: '/womens/shop/clothing/dresses' },\n        { name: 'Coats', href: '/womens/shop/clothing/coats' },\n        { name: 'Jackets', href: '/womens/shop/clothing/jackets' },\n        { name: 'Knitwear', href: '/womens/shop/clothing/knitwear' },\n        { name: 'Tops', href: '/womens/shop/clothing/tops' },\n        { name: 'Trousers', href: '/womens/shop/clothing/trousers' },\n        { name: 'Jeans', href: '/womens/shop/clothing/jeans' },\n        { name: 'Skirts', href: '/womens/shop/clothing/skirts' },\n      ]\n    },\n    {\n      title: 'Shoes',\n      links: [\n        { name: 'Shop all', href: '/womens/shop/shoes' },\n        { name: 'Boots', href: '/womens/shop/shoes/boots' },\n        { name: 'Heels', href: '/womens/shop/shoes/heels' },\n        { name: 'Flats', href: '/womens/shop/shoes/flats' },\n        { name: 'Trainers', href: '/womens/shop/shoes/sneakers' },\n        { name: 'Sandals', href: '/womens/shop/shoes/sandals' },\n      ]\n    },\n    {\n      title: 'Bags',\n      links: [\n        { name: 'Shop all', href: '/womens/shop/bags' },\n        { name: 'Tote bags', href: '/womens/shop/bags/tote-bags' },\n        { name: 'Shoulder bags', href: '/womens/shop/bags/shoulder-bags' },\n        { name: 'Cross-body bags', href: '/womens/shop/bags/cross-body-bags' },\n        { name: 'Clutch bags', href: '/womens/shop/bags/clutch-bags' },\n        { name: 'Mini bags', href: '/womens/shop/bags/mini-bags' },\n      ]\n    },\n    {\n      title: 'Accessories',\n      links: [\n        { name: 'View all', href: '/womens/shop/accessories' },\n        { name: 'Jewellery', href: '/womens/shop/jewellery-and-watches' },\n        { name: 'Sunglasses', href: '/womens/shop/accessories/sunglasses' },\n        { name: 'Belts', href: '/womens/shop/accessories/belts' },\n        { name: 'Scarves', href: '/womens/shop/accessories/scarves' },\n        { name: 'Hats', href: '/womens/shop/accessories/hats' },\n      ]\n    }\n  ];\n\n  const menCategories = [\n    {\n      title: 'Just In',\n      links: [\n        { name: 'Just in this month', href: '/mens/just-in/just-in-this-month' },\n        { name: 'Just in 7 days', href: '/mens/just-in/just-in-last-7-days' },\n        { name: 'Back in stock', href: '/mens/lists/back-in-stock' },\n        { name: 'Coming soon', href: '/mens/lists/coming-soon' },\n        { name: 'Exclusives', href: '/mens/lists/exclusives' },\n      ]\n    },\n    {\n      title: 'Clothing',\n      links: [\n        { name: 'Shop all', href: '/mens/shop/clothing' },\n        { name: 'Suits', href: '/mens/shop/clothing/suits' },\n        { name: 'Blazers', href: '/mens/shop/clothing/blazers' },\n        { name: 'Coats', href: '/mens/shop/clothing/coats' },\n        { name: 'Knitwear', href: '/mens/shop/clothing/knitwear' },\n        { name: 'Shirts', href: '/mens/shop/clothing/casual-shirts' },\n        { name: 'T-shirts', href: '/mens/shop/clothing/t-shirts' },\n        { name: 'Trousers', href: '/mens/shop/clothing/trousers' },\n        { name: 'Jeans', href: '/mens/shop/clothing/jeans' },\n      ]\n    },\n    {\n      title: 'Shoes',\n      links: [\n        { name: 'Shop all', href: '/mens/shop/shoes' },\n        { name: 'Formal shoes', href: '/mens/shop/shoes/dress-shoes' },\n        { name: 'Boots', href: '/mens/shop/shoes/boots' },\n        { name: 'Trainers', href: '/mens/shop/shoes/sneakers' },\n        { name: 'Loafers', href: '/mens/shop/shoes/loafers' },\n      ]\n    },\n    {\n      title: 'Bags',\n      links: [\n        { name: 'Shop all', href: '/mens/shop/bags' },\n        { name: 'Backpacks', href: '/mens/shop/bags/backpacks' },\n        { name: 'Travel bags', href: '/mens/shop/bags/travel-bags' },\n        { name: 'Briefcases', href: '/mens/shop/bags/briefcases' },\n        { name: 'Messenger bags', href: '/mens/shop/bags/messenger-bags' },\n      ]\n    },\n    {\n      title: 'Accessories',\n      links: [\n        { name: 'View all', href: '/mens/shop/accessories' },\n        { name: 'Watches', href: '/mens/shop/accessories/jewellery/watches' },\n        { name: 'Sunglasses', href: '/mens/shop/accessories/sunglasses' },\n        { name: 'Belts', href: '/mens/shop/accessories/belts' },\n        { name: 'Wallets', href: '/mens/shop/accessories/wallets' },\n        { name: 'Ties', href: '/mens/shop/accessories/ties' },\n      ]\n    }\n  ];\n\n  // Define categories for different menu types\n  const getMenuContent = () => {\n    switch (type) {\n      case 'women':\n        return {\n          categories: womenCategories,\n          designers: ['Gucci', 'Saint Laurent', 'Bottega Veneta', 'The Row', 'Khaite', 'Toteme'],\n          showProducts: true\n        };\n      case 'men':\n        return {\n          categories: menCategories,\n          designers: ['Tom Ford', 'Brunello Cucinelli', 'Stone Island', 'Thom Browne', 'Bottega Veneta', 'Gucci'],\n          showProducts: true\n        };\n      case 'just-in':\n        return {\n          categories: [\n            {\n              title: 'Women',\n              links: [\n                { name: 'Just in this month', href: '/womens/just-in/just-in-this-month' },\n                { name: 'Just in 7 days', href: '/womens/just-in/just-in-last-7-days' },\n                { name: 'Back in stock', href: '/womens/lists/back-in-stock' },\n                { name: 'Coming soon', href: '/womens/lists/coming-soon' },\n              ]\n            },\n            {\n              title: 'Men',\n              links: [\n                { name: 'Just in this month', href: '/mens/just-in/just-in-this-month' },\n                { name: 'Just in 7 days', href: '/mens/just-in/just-in-last-7-days' },\n                { name: 'Back in stock', href: '/mens/lists/back-in-stock' },\n                { name: 'Coming soon', href: '/mens/lists/coming-soon' },\n              ]\n            },\n            {\n              title: 'Categories',\n              links: [\n                { name: 'Clothing', href: '/just-in/clothing' },\n                { name: 'Shoes', href: '/just-in/shoes' },\n                { name: 'Bags', href: '/just-in/bags' },\n                { name: 'Accessories', href: '/just-in/accessories' },\n              ]\n            }\n          ],\n          designers: ['New Arrivals', 'Trending Now', 'Editor\\'s Picks'],\n          showProducts: true\n        };\n      case 'designers':\n        return {\n          categories: [\n            {\n              title: 'A-D',\n              links: [\n                { name: 'Acne Studios', href: '/designers/acne-studios' },\n                { name: 'Bottega Veneta', href: '/designers/bottega-veneta' },\n                { name: 'Celine', href: '/designers/celine' },\n                { name: 'Dior', href: '/designers/dior' },\n              ]\n            },\n            {\n              title: 'E-H',\n              links: [\n                { name: 'Fendi', href: '/designers/fendi' },\n                { name: 'Gucci', href: '/designers/gucci' },\n                { name: 'Hermes', href: '/designers/hermes' },\n              ]\n            },\n            {\n              title: 'I-M',\n              links: [\n                { name: 'Jacquemus', href: '/designers/jacquemus' },\n                { name: 'Khaite', href: '/designers/khaite' },\n                { name: 'Loewe', href: '/designers/loewe' },\n                { name: 'Moncler', href: '/designers/moncler' },\n              ]\n            },\n            {\n              title: 'N-S',\n              links: [\n                { name: 'Off-White', href: '/designers/off-white' },\n                { name: 'Prada', href: '/designers/prada' },\n                { name: 'Saint Laurent', href: '/designers/saint-laurent' },\n              ]\n            },\n            {\n              title: 'T-Z',\n              links: [\n                { name: 'The Row', href: '/designers/the-row' },\n                { name: 'Toteme', href: '/designers/toteme' },\n                { name: 'Valentino', href: '/designers/valentino' },\n                { name: 'Zimmermann', href: '/designers/zimmermann' },\n              ]\n            }\n          ],\n          designers: ['Featured Designers', 'New Designers', 'Luxury Brands'],\n          showProducts: true\n        };\n      default:\n        return {\n          categories: [\n            {\n              title: 'Women',\n              links: [\n                { name: 'Shop all', href: `/womens/${type}` },\n                { name: 'New arrivals', href: `/womens/${type}/new` },\n                { name: 'Best sellers', href: `/womens/${type}/best-sellers` },\n              ]\n            },\n            {\n              title: 'Men',\n              links: [\n                { name: 'Shop all', href: `/mens/${type}` },\n                { name: 'New arrivals', href: `/mens/${type}/new` },\n                { name: 'Best sellers', href: `/mens/${type}/best-sellers` },\n              ]\n            }\n          ],\n          designers: ['Featured Brands', 'New Arrivals', 'Best Sellers'],\n          showProducts: false\n        };\n    }\n  };\n\n  const menuContent = getMenuContent();\n  const { categories, designers: featuredDesigners, showProducts } = menuContent;\n\n  // Calculate grid columns based on content\n  const totalColumns = categories.length + 1; // +1 for featured designers\n  const gridCols = totalColumns <= 3 ? 'grid-cols-3' :\n                   totalColumns <= 4 ? 'grid-cols-4' :\n                   totalColumns <= 5 ? 'grid-cols-5' : 'grid-cols-6';\n\n  return (\n    <div className=\"max-w-7xl mx-auto px-4 py-8\">\n        <div className={`grid ${gridCols} gap-8`}>\n          {/* Categories */}\n          {categories.map((category, index) => (\n            <div key={index} className=\"space-y-4\">\n              <h3 className=\"font-semibold text-sm uppercase tracking-wide text-gray-900\">\n                {category.title}\n              </h3>\n              <ul className=\"space-y-2\">\n                {category.links.map((link, linkIndex) => (\n                  <li key={linkIndex}>\n                    <Link\n                      href={link.href}\n                      className=\"text-sm text-gray-600 hover:text-black transition-colors\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          ))}\n\n          {/* Featured Designers */}\n          <div className=\"space-y-4\">\n            <h3 className=\"font-semibold text-sm uppercase tracking-wide text-gray-900\">\n              Featured {type === 'designers' ? 'Collections' : 'Designers'}\n            </h3>\n            <ul className=\"space-y-2\">\n              {featuredDesigners.map((designer, index) => (\n                <li key={index}>\n                  <Link\n                    href={`/${type}/designers/${designer.toLowerCase().replace(/\\s+/g, '-')}`}\n                    className=\"text-sm text-gray-600 hover:text-black transition-colors\"\n                  >\n                    {designer}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n        </div>\n\n        {/* Featured Products Section - Only show for certain menu types */}\n        {showProducts && (\n          <div className=\"mt-8 pt-8 border-t border-gray-200\">\n            <div className=\"grid grid-cols-4 gap-6\">\n              {[1, 2, 3, 4].map((item) => (\n                <div key={item} className=\"group cursor-pointer\">\n                  <div className=\"aspect-square bg-gray-100 mb-3 overflow-hidden\">\n                    <div className=\"w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center\">\n                      <span className=\"text-gray-500 text-sm\">Product Image</span>\n                    </div>\n                  </div>\n                  <div className=\"space-y-1\">\n                    <p className=\"text-xs text-gray-500 uppercase tracking-wide\">Designer Name</p>\n                    <p className=\"text-sm font-medium\">Product Name</p>\n                    <p className=\"text-sm font-semibold\">£XXX</p>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n    </div>\n  );\n};\n\nexport default MegaMenu;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAQA,MAAM,WAAW,CAAC,EAAE,IAAI,EAAiB;IACvC,MAAM,kBAAkB;QACtB;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAsB,MAAM;gBAAqC;gBACzE;oBAAE,MAAM;oBAAkB,MAAM;gBAAsC;gBACtE;oBAAE,MAAM;oBAAiB,MAAM;gBAA8B;gBAC7D;oBAAE,MAAM;oBAAe,MAAM;gBAA4B;gBACzD;oBAAE,MAAM;oBAAc,MAAM;gBAA2B;aACxD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAY,MAAM;gBAAwB;gBAClD;oBAAE,MAAM;oBAAW,MAAM;gBAAgC;gBACzD;oBAAE,MAAM;oBAAS,MAAM;gBAA8B;gBACrD;oBAAE,MAAM;oBAAW,MAAM;gBAAgC;gBACzD;oBAAE,MAAM;oBAAY,MAAM;gBAAiC;gBAC3D;oBAAE,MAAM;oBAAQ,MAAM;gBAA6B;gBACnD;oBAAE,MAAM;oBAAY,MAAM;gBAAiC;gBAC3D;oBAAE,MAAM;oBAAS,MAAM;gBAA8B;gBACrD;oBAAE,MAAM;oBAAU,MAAM;gBAA+B;aACxD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAY,MAAM;gBAAqB;gBAC/C;oBAAE,MAAM;oBAAS,MAAM;gBAA2B;gBAClD;oBAAE,MAAM;oBAAS,MAAM;gBAA2B;gBAClD;oBAAE,MAAM;oBAAS,MAAM;gBAA2B;gBAClD;oBAAE,MAAM;oBAAY,MAAM;gBAA8B;gBACxD;oBAAE,MAAM;oBAAW,MAAM;gBAA6B;aACvD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAY,MAAM;gBAAoB;gBAC9C;oBAAE,MAAM;oBAAa,MAAM;gBAA8B;gBACzD;oBAAE,MAAM;oBAAiB,MAAM;gBAAkC;gBACjE;oBAAE,MAAM;oBAAmB,MAAM;gBAAoC;gBACrE;oBAAE,MAAM;oBAAe,MAAM;gBAAgC;gBAC7D;oBAAE,MAAM;oBAAa,MAAM;gBAA8B;aAC1D;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAY,MAAM;gBAA2B;gBACrD;oBAAE,MAAM;oBAAa,MAAM;gBAAqC;gBAChE;oBAAE,MAAM;oBAAc,MAAM;gBAAsC;gBAClE;oBAAE,MAAM;oBAAS,MAAM;gBAAiC;gBACxD;oBAAE,MAAM;oBAAW,MAAM;gBAAmC;gBAC5D;oBAAE,MAAM;oBAAQ,MAAM;gBAAgC;aACvD;QACH;KACD;IAED,MAAM,gBAAgB;QACpB;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAsB,MAAM;gBAAmC;gBACvE;oBAAE,MAAM;oBAAkB,MAAM;gBAAoC;gBACpE;oBAAE,MAAM;oBAAiB,MAAM;gBAA4B;gBAC3D;oBAAE,MAAM;oBAAe,MAAM;gBAA0B;gBACvD;oBAAE,MAAM;oBAAc,MAAM;gBAAyB;aACtD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAY,MAAM;gBAAsB;gBAChD;oBAAE,MAAM;oBAAS,MAAM;gBAA4B;gBACnD;oBAAE,MAAM;oBAAW,MAAM;gBAA8B;gBACvD;oBAAE,MAAM;oBAAS,MAAM;gBAA4B;gBACnD;oBAAE,MAAM;oBAAY,MAAM;gBAA+B;gBACzD;oBAAE,MAAM;oBAAU,MAAM;gBAAoC;gBAC5D;oBAAE,MAAM;oBAAY,MAAM;gBAA+B;gBACzD;oBAAE,MAAM;oBAAY,MAAM;gBAA+B;gBACzD;oBAAE,MAAM;oBAAS,MAAM;gBAA4B;aACpD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAY,MAAM;gBAAmB;gBAC7C;oBAAE,MAAM;oBAAgB,MAAM;gBAA+B;gBAC7D;oBAAE,MAAM;oBAAS,MAAM;gBAAyB;gBAChD;oBAAE,MAAM;oBAAY,MAAM;gBAA4B;gBACtD;oBAAE,MAAM;oBAAW,MAAM;gBAA2B;aACrD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAY,MAAM;gBAAkB;gBAC5C;oBAAE,MAAM;oBAAa,MAAM;gBAA4B;gBACvD;oBAAE,MAAM;oBAAe,MAAM;gBAA8B;gBAC3D;oBAAE,MAAM;oBAAc,MAAM;gBAA6B;gBACzD;oBAAE,MAAM;oBAAkB,MAAM;gBAAiC;aAClE;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAY,MAAM;gBAAyB;gBACnD;oBAAE,MAAM;oBAAW,MAAM;gBAA2C;gBACpE;oBAAE,MAAM;oBAAc,MAAM;gBAAoC;gBAChE;oBAAE,MAAM;oBAAS,MAAM;gBAA+B;gBACtD;oBAAE,MAAM;oBAAW,MAAM;gBAAiC;gBAC1D;oBAAE,MAAM;oBAAQ,MAAM;gBAA8B;aACrD;QACH;KACD;IAED,6CAA6C;IAC7C,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,YAAY;oBACZ,WAAW;wBAAC;wBAAS;wBAAiB;wBAAkB;wBAAW;wBAAU;qBAAS;oBACtF,cAAc;gBAChB;YACF,KAAK;gBACH,OAAO;oBACL,YAAY;oBACZ,WAAW;wBAAC;wBAAY;wBAAsB;wBAAgB;wBAAe;wBAAkB;qBAAQ;oBACvG,cAAc;gBAChB;YACF,KAAK;gBACH,OAAO;oBACL,YAAY;wBACV;4BACE,OAAO;4BACP,OAAO;gCACL;oCAAE,MAAM;oCAAsB,MAAM;gCAAqC;gCACzE;oCAAE,MAAM;oCAAkB,MAAM;gCAAsC;gCACtE;oCAAE,MAAM;oCAAiB,MAAM;gCAA8B;gCAC7D;oCAAE,MAAM;oCAAe,MAAM;gCAA4B;6BAC1D;wBACH;wBACA;4BACE,OAAO;4BACP,OAAO;gCACL;oCAAE,MAAM;oCAAsB,MAAM;gCAAmC;gCACvE;oCAAE,MAAM;oCAAkB,MAAM;gCAAoC;gCACpE;oCAAE,MAAM;oCAAiB,MAAM;gCAA4B;gCAC3D;oCAAE,MAAM;oCAAe,MAAM;gCAA0B;6BACxD;wBACH;wBACA;4BACE,OAAO;4BACP,OAAO;gCACL;oCAAE,MAAM;oCAAY,MAAM;gCAAoB;gCAC9C;oCAAE,MAAM;oCAAS,MAAM;gCAAiB;gCACxC;oCAAE,MAAM;oCAAQ,MAAM;gCAAgB;gCACtC;oCAAE,MAAM;oCAAe,MAAM;gCAAuB;6BACrD;wBACH;qBACD;oBACD,WAAW;wBAAC;wBAAgB;wBAAgB;qBAAkB;oBAC9D,cAAc;gBAChB;YACF,KAAK;gBACH,OAAO;oBACL,YAAY;wBACV;4BACE,OAAO;4BACP,OAAO;gCACL;oCAAE,MAAM;oCAAgB,MAAM;gCAA0B;gCACxD;oCAAE,MAAM;oCAAkB,MAAM;gCAA4B;gCAC5D;oCAAE,MAAM;oCAAU,MAAM;gCAAoB;gCAC5C;oCAAE,MAAM;oCAAQ,MAAM;gCAAkB;6BACzC;wBACH;wBACA;4BACE,OAAO;4BACP,OAAO;gCACL;oCAAE,MAAM;oCAAS,MAAM;gCAAmB;gCAC1C;oCAAE,MAAM;oCAAS,MAAM;gCAAmB;gCAC1C;oCAAE,MAAM;oCAAU,MAAM;gCAAoB;6BAC7C;wBACH;wBACA;4BACE,OAAO;4BACP,OAAO;gCACL;oCAAE,MAAM;oCAAa,MAAM;gCAAuB;gCAClD;oCAAE,MAAM;oCAAU,MAAM;gCAAoB;gCAC5C;oCAAE,MAAM;oCAAS,MAAM;gCAAmB;gCAC1C;oCAAE,MAAM;oCAAW,MAAM;gCAAqB;6BAC/C;wBACH;wBACA;4BACE,OAAO;4BACP,OAAO;gCACL;oCAAE,MAAM;oCAAa,MAAM;gCAAuB;gCAClD;oCAAE,MAAM;oCAAS,MAAM;gCAAmB;gCAC1C;oCAAE,MAAM;oCAAiB,MAAM;gCAA2B;6BAC3D;wBACH;wBACA;4BACE,OAAO;4BACP,OAAO;gCACL;oCAAE,MAAM;oCAAW,MAAM;gCAAqB;gCAC9C;oCAAE,MAAM;oCAAU,MAAM;gCAAoB;gCAC5C;oCAAE,MAAM;oCAAa,MAAM;gCAAuB;gCAClD;oCAAE,MAAM;oCAAc,MAAM;gCAAwB;6BACrD;wBACH;qBACD;oBACD,WAAW;wBAAC;wBAAsB;wBAAiB;qBAAgB;oBACnE,cAAc;gBAChB;YACF;gBACE,OAAO;oBACL,YAAY;wBACV;4BACE,OAAO;4BACP,OAAO;gCACL;oCAAE,MAAM;oCAAY,MAAM,CAAC,QAAQ,EAAE,MAAM;gCAAC;gCAC5C;oCAAE,MAAM;oCAAgB,MAAM,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC;gCAAC;gCACpD;oCAAE,MAAM;oCAAgB,MAAM,CAAC,QAAQ,EAAE,KAAK,aAAa,CAAC;gCAAC;6BAC9D;wBACH;wBACA;4BACE,OAAO;4BACP,OAAO;gCACL;oCAAE,MAAM;oCAAY,MAAM,CAAC,MAAM,EAAE,MAAM;gCAAC;gCAC1C;oCAAE,MAAM;oCAAgB,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC;gCAAC;gCAClD;oCAAE,MAAM;oCAAgB,MAAM,CAAC,MAAM,EAAE,KAAK,aAAa,CAAC;gCAAC;6BAC5D;wBACH;qBACD;oBACD,WAAW;wBAAC;wBAAmB;wBAAgB;qBAAe;oBAC9D,cAAc;gBAChB;QACJ;IACF;IAEA,MAAM,cAAc;IACpB,MAAM,EAAE,UAAU,EAAE,WAAW,iBAAiB,EAAE,YAAY,EAAE,GAAG;IAEnE,0CAA0C;IAC1C,MAAM,eAAe,WAAW,MAAM,GAAG,GAAG,4BAA4B;IACxE,MAAM,WAAW,gBAAgB,IAAI,gBACpB,gBAAgB,IAAI,gBACpB,gBAAgB,IAAI,gBAAgB;IAErD,qBACE,8OAAC;QAAI,WAAU;;0BACX,8OAAC;gBAAI,WAAW,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC;;oBAErC,WAAW,GAAG,CAAC,CAAC,UAAU,sBACzB,8OAAC;4BAAgB,WAAU;;8CACzB,8OAAC;oCAAG,WAAU;8CACX,SAAS,KAAK;;;;;;8CAEjB,8OAAC;oCAAG,WAAU;8CACX,SAAS,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,0BACzB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL;;;;;;;;;;;2BANL;;;;;kCAoBZ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAA8D;oCAChE,SAAS,cAAc,gBAAgB;;;;;;;0CAEnD,8OAAC;gCAAG,WAAU;0CACX,kBAAkB,GAAG,CAAC,CAAC,UAAU,sBAChC,8OAAC;kDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,CAAC,CAAC,EAAE,KAAK,WAAW,EAAE,SAAS,WAAW,GAAG,OAAO,CAAC,QAAQ,MAAM;4CACzE,WAAU;sDAET;;;;;;uCALI;;;;;;;;;;;;;;;;;;;;;;YAchB,8BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ;wBAAC;wBAAG;wBAAG;wBAAG;qBAAE,CAAC,GAAG,CAAC,CAAC,qBACjB,8OAAC;4BAAe,WAAU;;8CACxB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAwB;;;;;;;;;;;;;;;;8CAG5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAgD;;;;;;sDAC7D,8OAAC;4CAAE,WAAU;sDAAsB;;;;;;sDACnC,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;2BAT/B;;;;;;;;;;;;;;;;;;;;;AAkB1B;uCAEe", "debugId": null}}, {"offset": {"line": 803, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/matches-headless-ui/matches-fashion-clone/src/components/SearchModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { X, Search, TrendingUp } from 'lucide-react';\nimport Link from 'next/link';\n\ninterface SearchModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\ninterface Product {\n  id: number;\n  name: string;\n  brand: string;\n  price: number;\n  category: string;\n}\n\n// Mock products data - moved outside component to avoid dependency issues\nconst mockProducts: Product[] = [\n  { id: 1, name: 'Ophidia GG Supreme bag', brand: 'Gucci', price: 1200, category: 'Bags' },\n  { id: 2, name: 'Opyum pumps', brand: 'Saint Laurent', price: 895, category: 'Shoes' },\n  { id: 3, name: 'Cashmere coat', brand: 'The Row', price: 2890, category: 'Coats' },\n  { id: 4, name: 'Intrecciato wallet', brand: 'Bottega Veneta', price: 450, category: 'Accessories' },\n];\n\nconst SearchModal = ({ isOpen, onClose }: SearchModalProps) => {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [searchResults, setSearchResults] = useState<Product[]>([]);\n\n  // Mock search data\n  const trendingSearches = [\n    'Gucci bags',\n    'Saint Laurent shoes',\n    'The Row coats',\n    'Bottega Veneta',\n    'Khaite dresses',\n    'Designer jeans',\n    'Luxury sneakers',\n    'Evening dresses'\n  ];\n\n  useEffect(() => {\n    if (searchQuery.length > 2) {\n      // Simulate search results\n      const filtered = mockProducts.filter(product =>\n        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        product.brand.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        product.category.toLowerCase().includes(searchQuery.toLowerCase())\n      );\n      setSearchResults(filtered);\n    } else {\n      setSearchResults([]);\n    }\n  }, [searchQuery]);\n\n  useEffect(() => {\n    if (isOpen) {\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n\n    return () => {\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen]);\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 z-50 bg-white\">\n      {/* Header */}\n      <div className=\"border-b border-gray-200 p-4\">\n        <div className=\"max-w-4xl mx-auto flex items-center space-x-4\">\n          <div className=\"flex-1 relative\">\n            <Search className=\"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400\" size={20} />\n            <input\n              type=\"text\"\n              placeholder=\"Search for designers, products, or categories...\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"w-full pl-12 pr-4 py-3 text-lg border-none outline-none\"\n              autoFocus\n            />\n          </div>\n          <button\n            onClick={onClose}\n            className=\"p-2 hover:bg-gray-100 rounded-full transition-colors\"\n          >\n            <X size={24} />\n          </button>\n        </div>\n      </div>\n\n      {/* Content */}\n      <div className=\"max-w-4xl mx-auto p-4\">\n        {searchQuery.length === 0 ? (\n          /* Trending Searches */\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4 flex items-center\">\n              <TrendingUp size={20} className=\"mr-2\" />\n              Trending Searches\n            </h3>\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-3\">\n              {trendingSearches.map((search, index) => (\n                <button\n                  key={index}\n                  onClick={() => setSearchQuery(search)}\n                  className=\"text-left p-3 border border-gray-200 hover:border-black transition-colors text-sm\"\n                >\n                  {search}\n                </button>\n              ))}\n            </div>\n\n            {/* Popular Categories */}\n            <div className=\"mt-8\">\n              <h3 className=\"text-lg font-semibold mb-4\">Popular Categories</h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                <Link href=\"/womens/shop/clothing/dresses\" className=\"group\">\n                  <div className=\"aspect-[4/3] bg-gradient-to-br from-gray-100 to-gray-200 mb-3 flex items-center justify-center\">\n                    <span className=\"text-gray-500 text-sm\">Dresses</span>\n                  </div>\n                  <h4 className=\"font-medium group-hover:text-gray-600\">Women&apos;s Dresses</h4>\n                </Link>\n                <Link href=\"/womens/shop/bags\" className=\"group\">\n                  <div className=\"aspect-[4/3] bg-gradient-to-br from-gray-100 to-gray-200 mb-3 flex items-center justify-center\">\n                    <span className=\"text-gray-500 text-sm\">Bags</span>\n                  </div>\n                  <h4 className=\"font-medium group-hover:text-gray-600\">Designer Bags</h4>\n                </Link>\n                <Link href=\"/mens/shop/clothing/suits\" className=\"group\">\n                  <div className=\"aspect-[4/3] bg-gradient-to-br from-gray-100 to-gray-200 mb-3 flex items-center justify-center\">\n                    <span className=\"text-gray-500 text-sm\">Suits</span>\n                  </div>\n                  <h4 className=\"font-medium group-hover:text-gray-600\">Men&apos;s Suits</h4>\n                </Link>\n              </div>\n            </div>\n          </div>\n        ) : (\n          /* Search Results */\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">\n              Search Results for &quot;{searchQuery}&quot; ({searchResults.length})\n            </h3>\n            {searchResults.length > 0 ? (\n              <div className=\"space-y-4\">\n                {searchResults.map((product) => (\n                  <Link\n                    key={product.id}\n                    href={`/product/${product.id}`}\n                    className=\"flex items-center space-x-4 p-4 hover:bg-gray-50 transition-colors\"\n                    onClick={onClose}\n                  >\n                    <div className=\"w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center\">\n                      <span className=\"text-xs text-gray-500\">IMG</span>\n                    </div>\n                    <div className=\"flex-1\">\n                      <p className=\"text-xs text-gray-500 uppercase tracking-wide\">{product.brand}</p>\n                      <h4 className=\"font-medium\">{product.name}</h4>\n                      <p className=\"text-sm text-gray-600\">£{product.price}</p>\n                    </div>\n                    <div className=\"text-xs text-gray-500\">{product.category}</div>\n                  </Link>\n                ))}\n              </div>\n            ) : (\n              <div className=\"text-center py-12\">\n                <p className=\"text-gray-500 mb-4\">No results found for &quot;{searchQuery}&quot;</p>\n                <p className=\"text-sm text-gray-400\">Try searching for designers, product names, or categories</p>\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default SearchModal;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAJA;;;;;AAmBA,0EAA0E;AAC1E,MAAM,eAA0B;IAC9B;QAAE,IAAI;QAAG,MAAM;QAA0B,OAAO;QAAS,OAAO;QAAM,UAAU;IAAO;IACvF;QAAE,IAAI;QAAG,MAAM;QAAe,OAAO;QAAiB,OAAO;QAAK,UAAU;IAAQ;IACpF;QAAE,IAAI;QAAG,MAAM;QAAiB,OAAO;QAAW,OAAO;QAAM,UAAU;IAAQ;IACjF;QAAE,IAAI;QAAG,MAAM;QAAsB,OAAO;QAAkB,OAAO;QAAK,UAAU;IAAc;CACnG;AAED,MAAM,cAAc,CAAC,EAAE,MAAM,EAAE,OAAO,EAAoB;IACxD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IAEhE,mBAAmB;IACnB,MAAM,mBAAmB;QACvB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY,MAAM,GAAG,GAAG;YAC1B,0BAA0B;YAC1B,MAAM,WAAW,aAAa,MAAM,CAAC,CAAA,UACnC,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC3D,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC5D,QAAQ,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;YAEjE,iBAAiB;QACnB,OAAO;YACL,iBAAiB,EAAE;QACrB;IACF,GAAG;QAAC;KAAY;IAEhB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;QAEA,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;IACF,GAAG;QAAC;KAAO;IAEX,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;oCAAmE,MAAM;;;;;;8CAC3F,8OAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,WAAU;oCACV,SAAS;;;;;;;;;;;;sCAGb,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;0BAMf,8OAAC;gBAAI,WAAU;0BACZ,YAAY,MAAM,KAAK,IACtB,qBAAqB,iBACrB,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC,kNAAA,CAAA,aAAU;oCAAC,MAAM;oCAAI,WAAU;;;;;;gCAAS;;;;;;;sCAG3C,8OAAC;4BAAI,WAAU;sCACZ,iBAAiB,GAAG,CAAC,CAAC,QAAQ,sBAC7B,8OAAC;oCAEC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CAET;mCAJI;;;;;;;;;;sCAUX,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAgC,WAAU;;8DACnD,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;8DAE1C,8OAAC;oDAAG,WAAU;8DAAwC;;;;;;;;;;;;sDAExD,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAoB,WAAU;;8DACvC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;8DAE1C,8OAAC;oDAAG,WAAU;8DAAwC;;;;;;;;;;;;sDAExD,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAA4B,WAAU;;8DAC/C,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;8DAE1C,8OAAC;oDAAG,WAAU;8DAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAM9D,kBAAkB,iBAClB,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;;gCAA6B;gCACf;gCAAY;gCAAS,cAAc,MAAM;gCAAC;;;;;;;wBAErE,cAAc,MAAM,GAAG,kBACtB,8OAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC,wBAClB,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE;oCAC9B,WAAU;oCACV,SAAS;;sDAET,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;sDAE1C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAiD,QAAQ,KAAK;;;;;;8DAC3E,8OAAC;oDAAG,WAAU;8DAAe,QAAQ,IAAI;;;;;;8DACzC,8OAAC;oDAAE,WAAU;;wDAAwB;wDAAE,QAAQ,KAAK;;;;;;;;;;;;;sDAEtD,8OAAC;4CAAI,WAAU;sDAAyB,QAAQ,QAAQ;;;;;;;mCAbnD,QAAQ,EAAE;;;;;;;;;iDAkBrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;;wCAAqB;wCAA4B;wCAAY;;;;;;;8CAC1E,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrD;uCAEe", "debugId": null}}, {"offset": {"line": 1263, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/matches-headless-ui/matches-fashion-clone/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { Menu, X, Search, User, Settings, Heart, ShoppingBag } from 'lucide-react';\nimport MegaMenu from './MegaMenu';\nimport SearchModal from './SearchModal';\n\nconst Header = () => {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [activeMenu, setActiveMenu] = useState<string | null>(null);\n  const [isSearchOpen, setIsSearchOpen] = useState(false);\n  const [hoverTimeout, setHoverTimeout] = useState<NodeJS.Timeout | null>(null);\n\n  const toggleMobileMenu = () => {\n    setIsMobileMenuOpen(!isMobileMenuOpen);\n  };\n\n  const handleMenuHover = (menu: string) => {\n    if (hoverTimeout) {\n      clearTimeout(hoverTimeout);\n      setHoverTimeout(null);\n    }\n    setActiveMenu(menu);\n  };\n\n  const handleMenuLeave = () => {\n    const timeout = setTimeout(() => {\n      setActiveMenu(null);\n    }, 300); // Longer delay to allow moving to mega menu\n    setHoverTimeout(timeout);\n  };\n\n  return (\n    <header className=\"relative bg-white border-b border-gray-200\">\n      {/* Top Bar - Hidden on mobile */}\n      <div className=\"hidden md:block bg-black text-white text-xs py-2\">\n        <div className=\"max-w-7xl mx-auto px-4 flex justify-between items-center\">\n          <div className=\"flex space-x-6\">\n            <Link href=\"/help\" className=\"hover:underline\">Help Centre</Link>\n            <Link href=\"/delivery\" className=\"hover:underline\">Delivery</Link>\n            <Link href=\"/returns\" className=\"hover:underline\">Returns</Link>\n          </div>\n          <div className=\"flex space-x-6\">\n            <Link href=\"/stores\" className=\"hover:underline\">Visit Us</Link>\n            <Link href=\"/apps\" className=\"hover:underline\">Our Apps</Link>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Header */}\n      <div\n        className=\"max-w-7xl mx-auto px-4 relative\"\n        onMouseLeave={handleMenuLeave}\n      >\n        {/* First Line: Logo - Men - Women - Search Bar */}\n        <div className=\"flex items-center justify-between h-16 md:h-20\">\n          {/* Mobile Menu Button */}\n          <button\n            className=\"flex md:hidden p-2 mr-4\"\n            onClick={toggleMobileMenu}\n            aria-label=\"Toggle menu\"\n          >\n            {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}\n          </button>\n\n          {/* Left Side: Logo + Primary Navigation */}\n          <div className=\"flex items-center\">\n            {/* Logo */}\n            <Link href=\"/\" className=\"flex-shrink-0 mr-8\">\n              <span className=\"text-2xl md:text-3xl font-bold tracking-wider\">MATCHES</span>\n            </Link>\n\n            {/* Primary Categories - Men & Women */}\n            <nav className=\"hidden md:flex items-center space-x-8\">\n              <div\n                onMouseEnter={() => handleMenuHover('men')}\n              >\n                <Link\n                  href=\"/mens\"\n                  className=\"text-sm font-medium tracking-wide uppercase hover:text-gray-600 transition-colors\"\n                >\n                  Men\n                </Link>\n              </div>\n              <div\n                onMouseEnter={() => handleMenuHover('women')}\n              >\n                <Link\n                  href=\"/womens\"\n                  className=\"text-sm font-medium tracking-wide uppercase hover:text-gray-600 transition-colors\"\n                >\n                  Women\n                </Link>\n              </div>\n            </nav>\n          </div>\n\n          {/* Right Side: Search Bar + Icons */}\n          <div className=\"flex items-center space-x-4\">\n            {/* Search Bar */}\n            <div className=\"hidden md:flex items-center bg-gray-100 rounded-full px-4 py-2 w-80\">\n              <Search size={16} className=\"text-gray-400 mr-2\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search for products, designers...\"\n                className=\"bg-transparent text-sm flex-1 outline-none placeholder-gray-500\"\n                onClick={() => setIsSearchOpen(true)}\n                readOnly\n              />\n            </div>\n\n            {/* Mobile Search Button */}\n            <button\n              className=\"flex md:hidden p-2 hover:bg-gray-100 rounded-full transition-colors\"\n              onClick={() => setIsSearchOpen(true)}\n            >\n              <Search size={20} />\n            </button>\n\n            {/* Right Side Icons */}\n            <button className=\"p-2 hover:bg-gray-100 rounded-full transition-colors\">\n              <User size={20} />\n            </button>\n            <button className=\"p-2 hover:bg-gray-100 rounded-full transition-colors\">\n              <Settings size={20} />\n            </button>\n            <button className=\"p-2 hover:bg-gray-100 rounded-full transition-colors\">\n              <Heart size={20} />\n            </button>\n            <button className=\"p-2 hover:bg-gray-100 rounded-full transition-colors relative\">\n              <ShoppingBag size={20} />\n              <span className=\"absolute -top-1 -right-1 bg-black text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\">\n                0\n              </span>\n            </button>\n          </div>\n        </div>\n\n        {/* Second Line: Sub Categories */}\n        <div className=\"hidden md:block border-t border-gray-100 py-3\">\n          <nav className=\"flex items-center space-x-8\">\n            <div\n              onMouseEnter={() => handleMenuHover('just-in')}\n            >\n              <Link\n                href=\"/just-in\"\n                className=\"text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors\"\n              >\n                Just In\n              </Link>\n            </div>\n            <div\n              onMouseEnter={() => handleMenuHover('designers')}\n            >\n              <Link\n                href=\"/designers\"\n                className=\"text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors\"\n              >\n                Designers\n              </Link>\n            </div>\n            <div\n              onMouseEnter={() => handleMenuHover('clothing')}\n            >\n              <Link\n                href=\"/clothing\"\n                className=\"text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors\"\n              >\n                Clothing\n              </Link>\n            </div>\n            <div\n              onMouseEnter={() => handleMenuHover('dresses')}\n            >\n              <Link\n                href=\"/dresses\"\n                className=\"text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors\"\n              >\n                Dresses\n              </Link>\n            </div>\n            <div\n              onMouseEnter={() => handleMenuHover('shoes')}\n            >\n              <Link\n                href=\"/shoes\"\n                className=\"text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors\"\n              >\n                Shoes\n              </Link>\n            </div>\n            <div\n              onMouseEnter={() => handleMenuHover('bags')}\n            >\n              <Link\n                href=\"/bags\"\n                className=\"text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors\"\n              >\n                Bags\n              </Link>\n            </div>\n            <div\n              onMouseEnter={() => handleMenuHover('accessories')}\n            >\n              <Link\n                href=\"/accessories\"\n                className=\"text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors\"\n              >\n                Accessories\n              </Link>\n            </div>\n            <div\n              onMouseEnter={() => handleMenuHover('jewellery')}\n            >\n              <Link\n                href=\"/jewellery-watches\"\n                className=\"text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors\"\n              >\n                Jewellery & Watches\n              </Link>\n            </div>\n            <div\n              onMouseEnter={() => handleMenuHover('home')}\n            >\n              <Link\n                href=\"/home\"\n                className=\"text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors\"\n              >\n                Home\n              </Link>\n            </div>\n            <div\n              onMouseEnter={() => handleMenuHover('edits')}\n            >\n              <Link\n                href=\"/edits\"\n                className=\"text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors\"\n              >\n                Edits\n              </Link>\n            </div>\n            <div\n              onMouseEnter={() => handleMenuHover('outlet')}\n            >\n              <Link\n                href=\"/outlet\"\n                className=\"text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors\"\n              >\n                Outlet\n              </Link>\n            </div>\n          </nav>\n        </div>\n\n        {/* Unified Mega Menu Container - positioned relative to header container */}\n        {activeMenu && (\n          <div\n            className=\"absolute top-full left-0 right-0 bg-white shadow-xl border-t border-gray-200 z-50\"\n            onMouseEnter={() => handleMenuHover(activeMenu)}\n          >\n            <MegaMenu type={activeMenu as 'women' | 'men' | 'just-in' | 'designers' | 'clothing' | 'dresses' | 'shoes' | 'bags' | 'accessories' | 'jewellery' | 'home' | 'edits' | 'outlet'} />\n          </div>\n        )}\n      </div>\n\n      {/* Mobile Menu Overlay */}\n      {isMobileMenuOpen && (\n        <div className=\"fixed inset-0 z-50 md:hidden\">\n          <div className=\"fixed inset-0 bg-black bg-opacity-50\" onClick={toggleMobileMenu} />\n          <div className=\"fixed left-0 top-0 h-full w-80 bg-white shadow-xl\">\n            <div className=\"flex items-center justify-between p-4 border-b\">\n              <span className=\"text-xl font-bold\">MATCHES</span>\n              <button onClick={toggleMobileMenu}>\n                <X size={24} />\n              </button>\n            </div>\n            <nav className=\"p-4 overflow-y-auto\">\n              <div className=\"space-y-4\">\n                {/* Primary Categories */}\n                <div className=\"space-y-2\">\n                  <Link\n                    href=\"/womens\"\n                    className=\"block text-lg font-medium py-2 border-b border-gray-100\"\n                    onClick={toggleMobileMenu}\n                  >\n                    Women\n                  </Link>\n                  <Link\n                    href=\"/mens\"\n                    className=\"block text-lg font-medium py-2 border-b border-gray-100\"\n                    onClick={toggleMobileMenu}\n                  >\n                    Men\n                  </Link>\n                </div>\n\n                {/* Secondary Categories */}\n                <div className=\"pt-4 space-y-2\">\n                  <h3 className=\"text-sm font-semibold text-gray-500 uppercase tracking-wide mb-2\">Shop</h3>\n                  <Link href=\"/just-in\" className=\"block text-sm py-1 hover:text-gray-600\" onClick={toggleMobileMenu}>Just In</Link>\n                  <Link href=\"/designers\" className=\"block text-sm py-1 hover:text-gray-600\" onClick={toggleMobileMenu}>Designers</Link>\n                  <Link href=\"/clothing\" className=\"block text-sm py-1 hover:text-gray-600\" onClick={toggleMobileMenu}>Clothing</Link>\n                  <Link href=\"/dresses\" className=\"block text-sm py-1 hover:text-gray-600\" onClick={toggleMobileMenu}>Dresses</Link>\n                  <Link href=\"/shoes\" className=\"block text-sm py-1 hover:text-gray-600\" onClick={toggleMobileMenu}>Shoes</Link>\n                  <Link href=\"/bags\" className=\"block text-sm py-1 hover:text-gray-600\" onClick={toggleMobileMenu}>Bags</Link>\n                  <Link href=\"/accessories\" className=\"block text-sm py-1 hover:text-gray-600\" onClick={toggleMobileMenu}>Accessories</Link>\n                  <Link href=\"/jewellery-watches\" className=\"block text-sm py-1 hover:text-gray-600\" onClick={toggleMobileMenu}>Jewellery & Watches</Link>\n                  <Link href=\"/home\" className=\"block text-sm py-1 hover:text-gray-600\" onClick={toggleMobileMenu}>Home</Link>\n                  <Link href=\"/edits\" className=\"block text-sm py-1 hover:text-gray-600\" onClick={toggleMobileMenu}>Edits</Link>\n                  <Link href=\"/outlet\" className=\"block text-sm py-1 hover:text-gray-600\" onClick={toggleMobileMenu}>Outlet</Link>\n                </div>\n\n                {/* Account & Support */}\n                <div className=\"pt-4 space-y-2 border-t border-gray-200\">\n                  <h3 className=\"text-sm font-semibold text-gray-500 uppercase tracking-wide mb-2\">Account</h3>\n                  <Link href=\"/account\" className=\"block text-sm py-1 hover:text-gray-600\" onClick={toggleMobileMenu}>My Account</Link>\n                  <Link href=\"/settings\" className=\"block text-sm py-1 hover:text-gray-600\" onClick={toggleMobileMenu}>Settings</Link>\n                  <Link href=\"/help\" className=\"block text-sm py-1 hover:text-gray-600\" onClick={toggleMobileMenu}>Help Centre</Link>\n                  <Link href=\"/delivery\" className=\"block text-sm py-1 hover:text-gray-600\" onClick={toggleMobileMenu}>Delivery</Link>\n                </div>\n              </div>\n            </nav>\n          </div>\n        </div>\n      )}\n\n      {/* Search Modal */}\n      <SearchModal isOpen={isSearchOpen} onClose={() => setIsSearchOpen(false)} />\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AANA;;;;;;;AAQA,MAAM,SAAS;IACb,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IAExE,MAAM,mBAAmB;QACvB,oBAAoB,CAAC;IACvB;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,cAAc;YAChB,aAAa;YACb,gBAAgB;QAClB;QACA,cAAc;IAChB;IAEA,MAAM,kBAAkB;QACtB,MAAM,UAAU,WAAW;YACzB,cAAc;QAChB,GAAG,MAAM,4CAA4C;QACrD,gBAAgB;IAClB;IAEA,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAQ,WAAU;8CAAkB;;;;;;8CAC/C,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAY,WAAU;8CAAkB;;;;;;8CACnD,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAkB;;;;;;;;;;;;sCAEpD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAU,WAAU;8CAAkB;;;;;;8CACjD,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAQ,WAAU;8CAAkB;;;;;;;;;;;;;;;;;;;;;;;0BAMrD,8OAAC;gBACC,WAAU;gBACV,cAAc;;kCAGd,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCACC,WAAU;gCACV,SAAS;gCACT,cAAW;0CAEV,iCAAmB,8OAAC,4LAAA,CAAA,IAAC;oCAAC,MAAM;;;;;yDAAS,8OAAC,kMAAA,CAAA,OAAI;oCAAC,MAAM;;;;;;;;;;;0CAIpD,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDACvB,cAAA,8OAAC;4CAAK,WAAU;sDAAgD;;;;;;;;;;;kDAIlE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,cAAc,IAAM,gBAAgB;0DAEpC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;8DACX;;;;;;;;;;;0DAIH,8OAAC;gDACC,cAAc,IAAM,gBAAgB;0DAEpC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;0CAQP,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,MAAM;gDAAI,WAAU;;;;;;0DAC5B,8OAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,WAAU;gDACV,SAAS,IAAM,gBAAgB;gDAC/B,QAAQ;;;;;;;;;;;;kDAKZ,8OAAC;wCACC,WAAU;wCACV,SAAS,IAAM,gBAAgB;kDAE/B,cAAA,8OAAC,sMAAA,CAAA,SAAM;4CAAC,MAAM;;;;;;;;;;;kDAIhB,8OAAC;wCAAO,WAAU;kDAChB,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,MAAM;;;;;;;;;;;kDAEd,8OAAC;wCAAO,WAAU;kDAChB,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,MAAM;;;;;;;;;;;kDAElB,8OAAC;wCAAO,WAAU;kDAChB,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,MAAM;;;;;;;;;;;kDAEf,8OAAC;wCAAO,WAAU;;0DAChB,8OAAC,oNAAA,CAAA,cAAW;gDAAC,MAAM;;;;;;0DACnB,8OAAC;gDAAK,WAAU;0DAA6G;;;;;;;;;;;;;;;;;;;;;;;;kCAQnI,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,cAAc,IAAM,gBAAgB;8CAEpC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;8CAIH,8OAAC;oCACC,cAAc,IAAM,gBAAgB;8CAEpC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;8CAIH,8OAAC;oCACC,cAAc,IAAM,gBAAgB;8CAEpC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;8CAIH,8OAAC;oCACC,cAAc,IAAM,gBAAgB;8CAEpC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;8CAIH,8OAAC;oCACC,cAAc,IAAM,gBAAgB;8CAEpC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;8CAIH,8OAAC;oCACC,cAAc,IAAM,gBAAgB;8CAEpC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;8CAIH,8OAAC;oCACC,cAAc,IAAM,gBAAgB;8CAEpC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;8CAIH,8OAAC;oCACC,cAAc,IAAM,gBAAgB;8CAEpC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;8CAIH,8OAAC;oCACC,cAAc,IAAM,gBAAgB;8CAEpC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;8CAIH,8OAAC;oCACC,cAAc,IAAM,gBAAgB;8CAEpC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;8CAIH,8OAAC;oCACC,cAAc,IAAM,gBAAgB;8CAEpC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;oBAQN,4BACC,8OAAC;wBACC,WAAU;wBACV,cAAc,IAAM,gBAAgB;kCAEpC,cAAA,8OAAC,8HAAA,CAAA,UAAQ;4BAAC,MAAM;;;;;;;;;;;;;;;;;YAMrB,kCACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;wBAAuC,SAAS;;;;;;kCAC/D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAoB;;;;;;kDACpC,8OAAC;wCAAO,SAAS;kDACf,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,MAAM;;;;;;;;;;;;;;;;;0CAGb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS;8DACV;;;;;;8DAGD,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS;8DACV;;;;;;;;;;;;sDAMH,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAmE;;;;;;8DACjF,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;oDAAyC,SAAS;8DAAkB;;;;;;8DACpG,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAa,WAAU;oDAAyC,SAAS;8DAAkB;;;;;;8DACtG,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAY,WAAU;oDAAyC,SAAS;8DAAkB;;;;;;8DACrG,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;oDAAyC,SAAS;8DAAkB;;;;;;8DACpG,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,WAAU;oDAAyC,SAAS;8DAAkB;;;;;;8DAClG,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAQ,WAAU;oDAAyC,SAAS;8DAAkB;;;;;;8DACjG,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAe,WAAU;oDAAyC,SAAS;8DAAkB;;;;;;8DACxG,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAqB,WAAU;oDAAyC,SAAS;8DAAkB;;;;;;8DAC9G,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAQ,WAAU;oDAAyC,SAAS;8DAAkB;;;;;;8DACjG,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,WAAU;oDAAyC,SAAS;8DAAkB;;;;;;8DAClG,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAU,WAAU;oDAAyC,SAAS;8DAAkB;;;;;;;;;;;;sDAIrG,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAmE;;;;;;8DACjF,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;oDAAyC,SAAS;8DAAkB;;;;;;8DACpG,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAY,WAAU;oDAAyC,SAAS;8DAAkB;;;;;;8DACrG,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAQ,WAAU;oDAAyC,SAAS;8DAAkB;;;;;;8DACjG,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAY,WAAU;oDAAyC,SAAS;8DAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASjH,8OAAC,iIAAA,CAAA,UAAW;gBAAC,QAAQ;gBAAc,SAAS,IAAM,gBAAgB;;;;;;;;;;;;AAGxE;uCAEe", "debugId": null}}, {"offset": {"line": 2128, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/matches-headless-ui/matches-fashion-clone/src/components/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { Instagram, Facebook, Youtube, Twitter } from 'lucide-react';\n\nconst Footer = () => {\n  return (\n    <footer className=\"bg-white border-t border-gray-200\">\n      {/* Newsletter Section */}\n      <div className=\"bg-gray-50 py-12\">\n        <div className=\"max-w-7xl mx-auto px-4 text-center\">\n          <h2 className=\"text-2xl font-bold mb-4\">Stay in the know</h2>\n          <p className=\"text-gray-600 mb-6 max-w-md mx-auto\">\n            Be the first to discover new arrivals, exclusive collections, and styling tips\n          </p>\n          <div className=\"flex max-w-md mx-auto\">\n            <input\n              type=\"email\"\n              placeholder=\"Enter your email address\"\n              className=\"flex-1 px-4 py-3 border border-gray-300 focus:outline-none focus:border-black\"\n            />\n            <button className=\"luxury-button\">\n              Sign up\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Footer Content */}\n      <div className=\"max-w-7xl mx-auto px-4 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8\">\n          {/* MATCHES */}\n          <div className=\"lg:col-span-1\">\n            <h3 className=\"font-semibold text-sm uppercase tracking-wide mb-4\">MATCHES</h3>\n            <ul className=\"space-y-2\">\n              <li><Link href=\"/bio\" className=\"text-sm text-gray-600 hover:text-black\">About Us</Link></li>\n              <li><Link href=\"/careers\" className=\"text-sm text-gray-600 hover:text-black\">Careers</Link></li>\n              <li><Link href=\"/affiliates\" className=\"text-sm text-gray-600 hover:text-black\">Affiliates</Link></li>\n              <li><Link href=\"/press\" className=\"text-sm text-gray-600 hover:text-black\">Press</Link></li>\n            </ul>\n          </div>\n\n          {/* Services */}\n          <div className=\"lg:col-span-1\">\n            <h3 className=\"font-semibold text-sm uppercase tracking-wide mb-4\">Services</h3>\n            <ul className=\"space-y-2\">\n              <li><Link href=\"/private-shopping\" className=\"text-sm text-gray-600 hover:text-black\">Private Shopping</Link></li>\n              <li><Link href=\"/loyalty\" className=\"text-sm text-gray-600 hover:text-black\">Loyalty</Link></li>\n              <li><Link href=\"/rental\" className=\"text-sm text-gray-600 hover:text-black\">MATCHES Rental</Link></li>\n              <li><Link href=\"/gift-cards\" className=\"text-sm text-gray-600 hover:text-black\">Gift Cards</Link></li>\n            </ul>\n          </div>\n\n          {/* Legal */}\n          <div className=\"lg:col-span-1\">\n            <h3 className=\"font-semibold text-sm uppercase tracking-wide mb-4\">Legal</h3>\n            <ul className=\"space-y-2\">\n              <li><Link href=\"/terms\" className=\"text-sm text-gray-600 hover:text-black\">Terms and Conditions</Link></li>\n              <li><Link href=\"/privacy\" className=\"text-sm text-gray-600 hover:text-black\">Privacy Policy</Link></li>\n              <li><Link href=\"/cookies\" className=\"text-sm text-gray-600 hover:text-black\">Cookie Policy</Link></li>\n              <li><Link href=\"/modern-slavery\" className=\"text-sm text-gray-600 hover:text-black\">Modern Slavery Statement</Link></li>\n            </ul>\n          </div>\n\n          {/* Visit Us */}\n          <div className=\"lg:col-span-1\">\n            <h3 className=\"font-semibold text-sm uppercase tracking-wide mb-4\">Visit Us</h3>\n            <ul className=\"space-y-2\">\n              <li><Link href=\"/stores/5carlosplace\" className=\"text-sm text-gray-600 hover:text-black\">5 Carlos Place</Link></li>\n              <li><Link href=\"/stores/marylebone\" className=\"text-sm text-gray-600 hover:text-black\">Marylebone</Link></li>\n              <li><Link href=\"/stores/wimbledon\" className=\"text-sm text-gray-600 hover:text-black\">Wimbledon</Link></li>\n            </ul>\n          </div>\n\n          {/* Help Centre */}\n          <div className=\"lg:col-span-1\">\n            <h3 className=\"font-semibold text-sm uppercase tracking-wide mb-4\">Help Centre</h3>\n            <ul className=\"space-y-2\">\n              <li><Link href=\"/help\" className=\"text-sm text-gray-600 hover:text-black\">Help Centre</Link></li>\n              <li><Link href=\"/returns\" className=\"text-sm text-gray-600 hover:text-black\">Returning an item</Link></li>\n              <li><Link href=\"/delivery\" className=\"text-sm text-gray-600 hover:text-black\">Delivery</Link></li>\n              <li><Link href=\"/size-guide\" className=\"text-sm text-gray-600 hover:text-black\">Size Guide</Link></li>\n            </ul>\n          </div>\n\n          {/* Social Media & Apps */}\n          <div className=\"lg:col-span-1\">\n            <h3 className=\"font-semibold text-sm uppercase tracking-wide mb-4\">Follow Us</h3>\n            <div className=\"flex space-x-4 mb-6\">\n              <Link href=\"https://instagram.com/matches\" className=\"text-gray-600 hover:text-black\">\n                <Instagram size={20} />\n              </Link>\n              <Link href=\"https://facebook.com/matches\" className=\"text-gray-600 hover:text-black\">\n                <Facebook size={20} />\n              </Link>\n              <Link href=\"https://youtube.com/matches\" className=\"text-gray-600 hover:text-black\">\n                <Youtube size={20} />\n              </Link>\n              <Link href=\"https://twitter.com/matches\" className=\"text-gray-600 hover:text-black\">\n                <Twitter size={20} />\n              </Link>\n            </div>\n            <h4 className=\"font-semibold text-sm uppercase tracking-wide mb-4\">Our Apps</h4>\n            <div className=\"space-y-2\">\n              <Link href=\"#\" className=\"block\">\n                <div className=\"bg-black text-white px-3 py-2 text-xs rounded\">\n                  Download on the App Store\n                </div>\n              </Link>\n              <Link href=\"#\" className=\"block\">\n                <div className=\"bg-black text-white px-3 py-2 text-xs rounded\">\n                  Get it on Google Play\n                </div>\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Bottom Bar */}\n      <div className=\"border-t border-gray-200 py-6\">\n        <div className=\"max-w-7xl mx-auto px-4 flex flex-col md:flex-row justify-between items-center\">\n          <div className=\"flex items-center space-x-4 mb-4 md:mb-0\">\n            <span className=\"text-sm text-gray-600\">Shipping to</span>\n            <button className=\"text-sm font-medium border border-gray-300 px-3 py-1 hover:border-black\">\n              United Kingdom\n            </button>\n          </div>\n          <div className=\"text-sm text-gray-600\">\n            © Copyright 2024 MATCHES\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,SAAS;IACb,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0B;;;;;;sCACxC,8OAAC;4BAAE,WAAU;sCAAsC;;;;;;sCAGnD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,WAAU;;;;;;8CAEZ,8OAAC;oCAAO,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;0BAQxC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqD;;;;;;8CACnE,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAO,WAAU;0DAAyC;;;;;;;;;;;sDACzE,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAyC;;;;;;;;;;;sDAC7E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAc,WAAU;0DAAyC;;;;;;;;;;;sDAChF,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAyC;;;;;;;;;;;;;;;;;;;;;;;sCAK/E,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqD;;;;;;8CACnE,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAoB,WAAU;0DAAyC;;;;;;;;;;;sDACtF,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAyC;;;;;;;;;;;sDAC7E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAU,WAAU;0DAAyC;;;;;;;;;;;sDAC5E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAc,WAAU;0DAAyC;;;;;;;;;;;;;;;;;;;;;;;sCAKpF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqD;;;;;;8CACnE,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAyC;;;;;;;;;;;sDAC3E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAyC;;;;;;;;;;;sDAC7E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAyC;;;;;;;;;;;sDAC7E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAkB,WAAU;0DAAyC;;;;;;;;;;;;;;;;;;;;;;;sCAKxF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqD;;;;;;8CACnE,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAuB,WAAU;0DAAyC;;;;;;;;;;;sDACzF,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAqB,WAAU;0DAAyC;;;;;;;;;;;sDACvF,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAoB,WAAU;0DAAyC;;;;;;;;;;;;;;;;;;;;;;;sCAK1F,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqD;;;;;;8CACnE,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAyC;;;;;;;;;;;sDAC1E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAyC;;;;;;;;;;;sDAC7E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;0DAAyC;;;;;;;;;;;sDAC9E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAc,WAAU;0DAAyC;;;;;;;;;;;;;;;;;;;;;;;sCAKpF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqD;;;;;;8CACnE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAgC,WAAU;sDACnD,cAAA,8OAAC,4MAAA,CAAA,YAAS;gDAAC,MAAM;;;;;;;;;;;sDAEnB,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAA+B,WAAU;sDAClD,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,MAAM;;;;;;;;;;;sDAElB,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAA8B,WAAU;sDACjD,cAAA,8OAAC,wMAAA,CAAA,UAAO;gDAAC,MAAM;;;;;;;;;;;sDAEjB,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAA8B,WAAU;sDACjD,cAAA,8OAAC,wMAAA,CAAA,UAAO;gDAAC,MAAM;;;;;;;;;;;;;;;;;8CAGnB,8OAAC;oCAAG,WAAU;8CAAqD;;;;;;8CACnE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDACvB,cAAA,8OAAC;gDAAI,WAAU;0DAAgD;;;;;;;;;;;sDAIjE,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDACvB,cAAA,8OAAC;gDAAI,WAAU;0DAAgD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUzE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAwB;;;;;;8CACxC,8OAAC;oCAAO,WAAU;8CAA0E;;;;;;;;;;;;sCAI9F,8OAAC;4BAAI,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;;AAOjD;uCAEe", "debugId": null}}, {"offset": {"line": 2832, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/matches-headless-ui/matches-fashion-clone/src/components/QCTestingPanel.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Monitor, Smartphone, Tablet, Eye, X } from 'lucide-react';\n\nconst QCTestingPanel = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [currentBreakpoint, setCurrentBreakpoint] = useState('desktop');\n\n  const breakpoints = [\n    { id: 'mobile', name: 'Mobile', width: '375px', icon: Smartphone },\n    { id: 'tablet', name: 'Tablet', width: '768px', icon: Tablet },\n    { id: 'desktop', name: 'Desktop', width: '1920px', icon: Monitor },\n  ];\n\n  const testPages = [\n    { name: 'Homepage', path: '/' },\n    { name: 'Women\\'s Landing', path: '/womens' },\n    { name: 'Women\\'s Clothing', path: '/womens/shop/clothing' },\n    { name: 'Men\\'s Landing', path: '/mens' },\n    { name: 'Men\\'s Clothing', path: '/mens/shop/clothing' },\n  ];\n\n  const qcChecklist = [\n    'Header navigation is properly aligned',\n    'Logo is clearly visible and properly sized',\n    'Mega menus display correctly on hover',\n    'Product grids maintain proper spacing',\n    'Typography hierarchy is consistent',\n    'Interactive elements have proper hover states',\n    'Footer content is well-organized',\n    'Mobile navigation works smoothly',\n    'Search functionality is accessible',\n    'Product cards display correctly',\n  ];\n\n  if (!isOpen) {\n    return (\n      <button\n        onClick={() => setIsOpen(true)}\n        className=\"fixed bottom-4 right-4 bg-black text-white p-3 rounded-full shadow-lg hover:bg-gray-800 transition-colors z-50\"\n        title=\"Open QC Testing Panel\"\n      >\n        <Eye size={20} />\n      </button>\n    );\n  }\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4\">\n      <div className=\"bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b\">\n          <h2 className=\"text-xl font-bold\">QC Testing Panel - Luxury Fashion Standards</h2>\n          <button\n            onClick={() => setIsOpen(false)}\n            className=\"p-2 hover:bg-gray-100 rounded-full\"\n          >\n            <X size={20} />\n          </button>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-6 space-y-6\">\n          {/* Breakpoint Testing */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Responsive Breakpoint Testing</h3>\n            <div className=\"grid grid-cols-3 gap-4 mb-4\">\n              {breakpoints.map((bp) => {\n                const IconComponent = bp.icon;\n                return (\n                  <button\n                    key={bp.id}\n                    onClick={() => setCurrentBreakpoint(bp.id)}\n                    className={`p-4 border rounded-lg flex flex-col items-center space-y-2 transition-colors ${\n                      currentBreakpoint === bp.id\n                        ? 'border-black bg-gray-50'\n                        : 'border-gray-200 hover:border-gray-400'\n                    }`}\n                  >\n                    <IconComponent size={24} />\n                    <span className=\"font-medium\">{bp.name}</span>\n                    <span className=\"text-sm text-gray-500\">{bp.width}</span>\n                  </button>\n                );\n              })}\n            </div>\n            <p className=\"text-sm text-gray-600\">\n              Current testing viewport: <strong>{breakpoints.find(bp => bp.id === currentBreakpoint)?.width}</strong>\n            </p>\n          </div>\n\n          {/* Page Testing */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Page Testing</h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n              {testPages.map((page, index) => (\n                <a\n                  key={index}\n                  href={page.path}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"p-3 border border-gray-200 rounded hover:border-black transition-colors text-sm\"\n                >\n                  {page.name} →\n                </a>\n              ))}\n            </div>\n          </div>\n\n          {/* QC Checklist */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Luxury Fashion QC Checklist</h3>\n            <div className=\"space-y-2\">\n              {qcChecklist.map((item, index) => (\n                <label key={index} className=\"flex items-center space-x-3\">\n                  <input\n                    type=\"checkbox\"\n                    className=\"w-4 h-4 text-black border-gray-300 rounded focus:ring-black\"\n                  />\n                  <span className=\"text-sm\">{item}</span>\n                </label>\n              ))}\n            </div>\n          </div>\n\n          {/* Design Standards */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Luxury Fashion Design Standards</h3>\n            <div className=\"bg-gray-50 p-4 rounded-lg space-y-3\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\">\n                <div>\n                  <h4 className=\"font-medium mb-2\">Typography</h4>\n                  <ul className=\"space-y-1 text-gray-600\">\n                    <li>• Clean, geometric sans-serif fonts</li>\n                    <li>• Consistent hierarchy and spacing</li>\n                    <li>• Uppercase tracking for headings</li>\n                  </ul>\n                </div>\n                <div>\n                  <h4 className=\"font-medium mb-2\">Layout</h4>\n                  <ul className=\"space-y-1 text-gray-600\">\n                    <li>• No rounded corners (clean geometric design)</li>\n                    <li>• Sophisticated spacing hierarchy</li>\n                    <li>• Pixel-perfect alignment</li>\n                  </ul>\n                </div>\n                <div>\n                  <h4 className=\"font-medium mb-2\">Interactions</h4>\n                  <ul className=\"space-y-1 text-gray-600\">\n                    <li>• Smooth hover transitions</li>\n                    <li>• Luxury micro-interactions</li>\n                    <li>• Responsive touch targets</li>\n                  </ul>\n                </div>\n                <div>\n                  <h4 className=\"font-medium mb-2\">Content</h4>\n                  <ul className=\"space-y-1 text-gray-600\">\n                    <li>• High-quality product imagery</li>\n                    <li>• Rich mega menu layouts</li>\n                    <li>• Editorial content integration</li>\n                  </ul>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Testing Instructions */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Testing Instructions</h3>\n            <div className=\"bg-blue-50 p-4 rounded-lg\">\n              <ol className=\"list-decimal list-inside space-y-2 text-sm\">\n                <li>Test each page at all three breakpoints (375px, 768px, 1920px)</li>\n                <li>Verify navigation functionality and mega menu interactions</li>\n                <li>Check product grid layouts and card hover states</li>\n                <li>Ensure search modal opens and functions correctly</li>\n                <li>Validate footer content organization and links</li>\n                <li>Test mobile navigation and touch interactions</li>\n                <li>Verify pixel-perfect alignment and spacing</li>\n                <li>Check for luxury fashion aesthetic consistency</li>\n              </ol>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default QCTestingPanel;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,iBAAiB;IACrB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,cAAc;QAClB;YAAE,IAAI;YAAU,MAAM;YAAU,OAAO;YAAS,MAAM,8MAAA,CAAA,aAAU;QAAC;QACjE;YAAE,IAAI;YAAU,MAAM;YAAU,OAAO;YAAS,MAAM,sMAAA,CAAA,SAAM;QAAC;QAC7D;YAAE,IAAI;YAAW,MAAM;YAAW,OAAO;YAAU,MAAM,wMAAA,CAAA,UAAO;QAAC;KAClE;IAED,MAAM,YAAY;QAChB;YAAE,MAAM;YAAY,MAAM;QAAI;QAC9B;YAAE,MAAM;YAAoB,MAAM;QAAU;QAC5C;YAAE,MAAM;YAAqB,MAAM;QAAwB;QAC3D;YAAE,MAAM;YAAkB,MAAM;QAAQ;QACxC;YAAE,MAAM;YAAmB,MAAM;QAAsB;KACxD;IAED,MAAM,cAAc;QAClB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,IAAI,CAAC,QAAQ;QACX,qBACE,8OAAC;YACC,SAAS,IAAM,UAAU;YACzB,WAAU;YACV,OAAM;sBAEN,cAAA,8OAAC,gMAAA,CAAA,MAAG;gBAAC,MAAM;;;;;;;;;;;IAGjB;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoB;;;;;;sCAClC,8OAAC;4BACC,SAAS,IAAM,UAAU;4BACzB,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,MAAM;;;;;;;;;;;;;;;;;8BAKb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC;wCAChB,MAAM,gBAAgB,GAAG,IAAI;wCAC7B,qBACE,8OAAC;4CAEC,SAAS,IAAM,qBAAqB,GAAG,EAAE;4CACzC,WAAW,CAAC,6EAA6E,EACvF,sBAAsB,GAAG,EAAE,GACvB,4BACA,yCACJ;;8DAEF,8OAAC;oDAAc,MAAM;;;;;;8DACrB,8OAAC;oDAAK,WAAU;8DAAe,GAAG,IAAI;;;;;;8DACtC,8OAAC;oDAAK,WAAU;8DAAyB,GAAG,KAAK;;;;;;;2CAV5C,GAAG,EAAE;;;;;oCAahB;;;;;;8CAEF,8OAAC;oCAAE,WAAU;;wCAAwB;sDACT,8OAAC;sDAAQ,YAAY,IAAI,CAAC,CAAA,KAAM,GAAG,EAAE,KAAK,oBAAoB;;;;;;;;;;;;;;;;;;sCAK5F,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;8CACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC;4CAEC,MAAM,KAAK,IAAI;4CACf,QAAO;4CACP,KAAI;4CACJ,WAAU;;gDAET,KAAK,IAAI;gDAAC;;2CANN;;;;;;;;;;;;;;;;sCAab,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC;4CAAkB,WAAU;;8DAC3B,8OAAC;oDACC,MAAK;oDACL,WAAU;;;;;;8DAEZ,8OAAC;oDAAK,WAAU;8DAAW;;;;;;;2CALjB;;;;;;;;;;;;;;;;sCAYlB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAmB;;;;;;kEACjC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;;;;;;;;;;;;;0DAGR,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAmB;;;;;;kEACjC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;;;;;;;;;;;;;0DAGR,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAmB;;;;;;kEACjC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;;;;;;;;;;;;;0DAGR,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAmB;;;;;;kEACjC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQd,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpB;uCAEe", "debugId": null}}]}