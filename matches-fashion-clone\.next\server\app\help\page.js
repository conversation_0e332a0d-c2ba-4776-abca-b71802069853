"use strict";(()=>{var e={};e.id=728,e.ids=[728],e.modules={846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{e.exports=require("path")},4421:(e,r,s)=>{s.r(r),s.d(r,{GlobalError:()=>l.a,__next_app__:()=>h,pages:()=>d,routeModule:()=>m,tree:()=>c});var a=s(5239),t=s(8088),n=s(8170),l=s.n(n),i=s(893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(r,o);let c={children:["",{children:["help",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,6336)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\matches-headless-ui\\matches-fashion-clone\\src\\app\\help\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\matches-headless-ui\\matches-fashion-clone\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,4413)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\matches-headless-ui\\matches-fashion-clone\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\matches-headless-ui\\matches-fashion-clone\\src\\app\\help\\page.tsx"],h={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/help/page",pathname:"/help",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},6336:(e,r,s)=>{s.r(r),s.d(r,{default:()=>l});var a=s(7413),t=s(4536),n=s.n(t);function l(){return(0,a.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 py-4",children:(0,a.jsxs)("nav",{className:"text-sm text-gray-500",children:[(0,a.jsx)(n(),{href:"/",className:"hover:text-black",children:"Home"}),(0,a.jsx)("span",{className:"mx-2",children:"/"}),(0,a.jsx)("span",{className:"text-black",children:"Help Centre"})]})}),(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 py-12",children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[(0,a.jsx)("h1",{className:"text-4xl font-bold mb-4",children:"Help Centre"}),(0,a.jsx)("p",{className:"text-gray-600 max-w-2xl mx-auto",children:"Find answers to your questions about shopping, delivery, returns, and more. Our customer service team is here to help you with your luxury shopping experience."})]}),(0,a.jsx)("div",{className:"max-w-2xl mx-auto mb-12",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:"text",placeholder:"Search for help topics...",className:"w-full px-6 py-4 border border-gray-300 focus:outline-none focus:border-black text-lg"}),(0,a.jsx)("button",{className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400",children:"Search"})]})}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12",children:[{title:"Orders & Delivery",links:[{name:"Track your order",href:"/help/track-order"},{name:"Delivery information",href:"/delivery"},{name:"Delivery charges",href:"/help/delivery-charges"},{name:"International delivery",href:"/help/international-delivery"}]},{title:"Returns & Exchanges",links:[{name:"Return policy",href:"/returns"},{name:"How to return",href:"/help/how-to-return"},{name:"Exchanges",href:"/help/exchanges"},{name:"Refunds",href:"/help/refunds"}]},{title:"Account & Shopping",links:[{name:"Create an account",href:"/help/create-account"},{name:"Manage your account",href:"/help/manage-account"},{name:"Size guide",href:"/size-guide"},{name:"Product care",href:"/help/product-care"}]},{title:"Payment & Pricing",links:[{name:"Payment methods",href:"/help/payment-methods"},{name:"Currency & pricing",href:"/help/currency-pricing"},{name:"Gift cards",href:"/gift-cards"},{name:"Promotional codes",href:"/help/promotional-codes"}]}].map((e,r)=>(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"font-semibold text-lg",children:e.title}),(0,a.jsx)("ul",{className:"space-y-2",children:e.links.map((e,r)=>(0,a.jsx)("li",{children:(0,a.jsx)(n(),{href:e.href,className:"text-gray-600 hover:text-black transition-colors text-sm",children:e.name})},r))})]},r))}),(0,a.jsxs)("div",{className:"bg-gray-50 p-8 text-center",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"Still need help?"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"Our customer service team is available to assist you with any questions."}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4",children:[(0,a.jsx)("button",{className:"luxury-button",children:"Live Chat"}),(0,a.jsx)("button",{className:"luxury-button-outline",children:"Email Us"})]}),(0,a.jsx)("div",{className:"mt-6 text-sm text-gray-500",children:(0,a.jsx)("p",{children:"Customer Service Hours: Monday - Friday, 9AM - 6PM GMT"})})]})]})]})}},9121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{e.exports=require("url")}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),a=r.X(0,[447,304,430],()=>s(4421));module.exports=a})();