1:"$Sreact.fragment"
2:I[8503,["874","static/chunks/874-4ab02debfaf779a6.js","177","static/chunks/app/layout-9b90e7ab0b316405.js"],"default"]
3:I[7555,[],""]
4:I[1295,[],""]
5:I[6874,["874","static/chunks/874-4ab02debfaf779a6.js","762","static/chunks/app/returns/page-a9c1037842cad80c.js"],""]
6:I[6987,["874","static/chunks/874-4ab02debfaf779a6.js","177","static/chunks/app/layout-9b90e7ab0b316405.js"],"default"]
7:I[2277,["874","static/chunks/874-4ab02debfaf779a6.js","177","static/chunks/app/layout-9b90e7ab0b316405.js"],"default"]
8:I[9665,[],"MetadataBoundary"]
a:I[9665,[],"OutletBoundary"]
d:I[4911,[],"AsyncMetadataOutlet"]
f:I[9665,[],"ViewportBoundary"]
11:I[6614,[],""]
:HL["/_next/static/css/1e19055866d1f255.css","style"]
0:{"P":null,"b":"GfHiHsXNIhYeWhao3WpiJ","p":"","c":["","returns"],"i":false,"f":[[["",{"children":["returns",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/1e19055866d1f255.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","children":["$","body",null,{"className":"min-h-screen flex flex-col antialiased","children":[["$","$L2",null,{}],["$","main",null,{"className":"flex-1","children":["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[["$","div",null,{"className":"min-h-screen flex items-center justify-center bg-white","children":["$","div",null,{"className":"text-center max-w-md mx-auto px-4","children":[["$","h1",null,{"className":"text-6xl font-bold mb-4","children":"404"}],["$","h2",null,{"className":"text-2xl font-semibold mb-4","children":"Page Not Found"}],["$","p",null,{"className":"text-gray-600 mb-8","children":"The page you are looking for might have been removed, had its name changed, or is temporarily unavailable."}],["$","div",null,{"className":"space-y-4","children":[["$","$L5",null,{"href":"/","className":"luxury-button block","children":"Return Home"}],["$","div",null,{"className":"flex justify-center space-x-4 text-sm","children":[["$","$L5",null,{"href":"/womens","className":"text-gray-600 hover:text-black","children":"Shop Women's"}],["$","$L5",null,{"href":"/mens","className":"text-gray-600 hover:text-black","children":"Shop Men's"}]]}]]}]]}]}],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}],["$","$L6",null,{}],["$","$L7",null,{}]]}]}]]}],{"children":["returns",["$","$1","c",{"children":[null,["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","div",null,{"className":"min-h-screen bg-white","children":[["$","div",null,{"className":"max-w-7xl mx-auto px-4 py-4","children":["$","nav",null,{"className":"text-sm text-gray-500","children":[["$","$L5",null,{"href":"/","className":"hover:text-black","children":"Home"}],["$","span",null,{"className":"mx-2","children":"/"}],["$","span",null,{"className":"text-black","children":"Returns"}]]}]}],["$","div",null,{"className":"max-w-4xl mx-auto px-4 py-12","children":[["$","h1",null,{"className":"text-4xl font-bold mb-8","children":"Returns & Exchanges"}],["$","div",null,{"className":"space-y-8","children":[["$","section",null,{"children":[["$","h2",null,{"className":"text-2xl font-semibold mb-4","children":"Return Policy"}],["$","div",null,{"className":"bg-gray-50 p-6 space-y-4","children":[["$","p",null,{"className":"text-gray-600","children":"We want you to be completely satisfied with your purchase. If you're not happy with your order, you can return it within 28 days of delivery for a full refund."}],["$","ul",null,{"className":"list-disc list-inside space-y-2 text-gray-600","children":[["$","li",null,{"children":"Items must be in original condition with all tags attached"}],["$","li",null,{"children":"Items must be unworn and in original packaging"}],["$","li",null,{"children":"Personalized or made-to-order items cannot be returned"}],["$","li",null,{"children":"Underwear, swimwear, and pierced jewelry cannot be returned for hygiene reasons"}]]}]]}]]}],["$","section",null,{"children":[["$","h2",null,{"className":"text-2xl font-semibold mb-4","children":"How to Return"}],["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-2 gap-8","children":[["$","div",null,{"children":[["$","h3",null,{"className":"font-medium mb-3","children":"UK Returns"}],["$","ol",null,{"className":"list-decimal list-inside space-y-2 text-sm text-gray-600","children":[["$","li",null,{"children":"Log into your account and select the items to return"}],["$","li",null,{"children":"Print your prepaid return label"}],["$","li",null,{"children":"Package items securely in original packaging"}],["$","li",null,{"children":"Attach the return label and drop off at any Royal Mail post office"}]]}],["$","p",null,{"className":"text-sm font-medium mt-3","children":"FREE returns for UK customers"}]]}],["$","div",null,{"children":[["$","h3",null,{"className":"font-medium mb-3","children":"International Returns"}],["$","ol",null,{"className":"list-decimal list-inside space-y-2 text-sm text-gray-600","children":[["$","li",null,{"children":"Contact our customer service team"}],["$","li",null,{"children":"We'll provide return instructions and label"}],["$","li",null,{"children":"Package items securely"}],["$","li",null,{"children":"Send via your local postal service"}]]}],["$","p",null,{"className":"text-sm font-medium mt-3","children":"Return shipping costs apply"}]]}]]}]]}],["$","section",null,{"children":[["$","h2",null,{"className":"text-2xl font-semibold mb-4","children":"Exchanges"}],["$","div",null,{"className":"bg-gray-50 p-6","children":[["$","p",null,{"className":"text-gray-600 mb-4","children":"We don't offer direct exchanges. To exchange an item, please return your original purchase and place a new order for the item you want."}],["$","p",null,{"className":"text-sm text-gray-600","children":"This ensures you receive your new item as quickly as possible and aren't charged twice."}]]}]]}],["$","section",null,{"children":[["$","h2",null,{"className":"text-2xl font-semibold mb-4","children":"Refunds"}],["$","div",null,{"className":"space-y-4","children":[["$","p",null,{"className":"text-gray-600","children":"Once we receive your return, we'll inspect the items and process your refund within 5-10 working days."}],["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-2 gap-6","children":[["$","div",null,{"children":[["$","h3",null,{"className":"font-medium mb-2","children":"Refund Methods"}],["$","ul",null,{"className":"text-sm text-gray-600 space-y-1","children":[["$","li",null,{"children":"• Original payment method"}],["$","li",null,{"children":"• Store credit (if preferred)"}],["$","li",null,{"children":"• Gift card (for gift purchases)"}]]}]]}],["$","div",null,{"children":[["$","h3",null,{"className":"font-medium mb-2","children":"Processing Times"}],["$","ul",null,{"className":"text-sm text-gray-600 space-y-1","children":[["$","li",null,{"children":"• Credit/Debit cards: 3-5 working days"}],["$","li",null,{"children":"• PayPal: 1-2 working days"}],["$","li",null,{"children":"• Bank transfer: 5-7 working days"}]]}]]}]]}]]}]]}],["$","section",null,{"children":[["$","h2",null,{"className":"text-2xl font-semibold mb-4","children":"Damaged or Faulty Items"}],["$","div",null,{"className":"bg-red-50 border border-red-200 p-6","children":[["$","p",null,{"className":"text-gray-700 mb-4","children":"If you receive a damaged or faulty item, please contact us immediately. We'll arrange for a replacement or full refund, including return shipping costs."}],["$","p",null,{"className":"text-sm text-gray-600","children":"Please take photos of any damage and include them when contacting our customer service team."}]]}]]}],["$","section",null,{"className":"bg-gray-50 p-6 text-center","children":[["$","h3",null,{"className":"font-semibold mb-2","children":"Need help with a return?"}],["$","p",null,{"className":"text-gray-600 mb-4","children":"Our customer service team is here to help"}],["$","div",null,{"className":"flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4","children":[["$","$L5",null,{"href":"/help","className":"luxury-button-outline","children":"Contact Us"}],["$","button",null,{"className":"luxury-button","children":"Start Return"}]]}]]}]]}]]}]]}],["$","$L8",null,{"children":"$L9"}],null,["$","$La",null,{"children":["$Lb","$Lc",["$","$Ld",null,{"promise":"$@e"}]]}]]}],{},null,false]},null,false]},null,false],["$","$1","h",{"children":[null,["$","$1","tueO0ld__Btdv-8QNu9Nt",{"children":[["$","$Lf",null,{"children":"$L10"}],null]}],null]}],false]],"m":"$undefined","G":["$11","$undefined"],"s":false,"S":true}
12:"$Sreact.suspense"
13:I[4911,[],"AsyncMetadata"]
9:["$","$12",null,{"fallback":null,"children":["$","$L13",null,{"promise":"$@14"}]}]
c:null
10:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
b:null
14:{"metadata":[["$","title","0",{"children":"Luxury Fashion | Designer Clothing, Bags & Shoes | MATCHES UK"}],["$","meta","1",{"name":"description","content":"Discover luxury fashion at MATCHES. Shop designer clothing, bags, shoes and accessories from over 450 established and innovative designer brands."}],["$","meta","2",{"name":"keywords","content":"luxury fashion, designer clothing, designer bags, designer shoes, luxury accessories, high-end fashion"}],["$","link","3",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}]],"error":null,"digest":"$undefined"}
e:{"metadata":"$14:metadata","error":null,"digest":"$undefined"}
