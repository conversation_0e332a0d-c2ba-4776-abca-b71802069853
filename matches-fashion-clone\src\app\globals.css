@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #000000;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: 'Helvetica Neue', Arial, sans-serif;
  --font-mono: 'Monaco', '<PERSON>lo', monospace;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
  margin: 0;
  padding: 0;
  line-height: 1.5;
}

/* Custom scrollbar for luxury feel */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Luxury button styles */
.luxury-button {
  @apply px-6 py-3 bg-black text-white text-sm font-medium tracking-wide uppercase transition-all duration-300 hover:bg-gray-800;
}

.luxury-button-outline {
  @apply px-6 py-3 border border-black text-black text-sm font-medium tracking-wide uppercase transition-all duration-300 hover:bg-black hover:text-white;
}

/* Hide scrollbar for product carousel */
.hide-scrollbar {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;  /* Safari and Chrome */
}
